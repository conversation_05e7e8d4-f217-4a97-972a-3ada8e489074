part of xr_helper;

abstract class BaseApiServices with XNetworkHelper {
  Future<dynamic> getResponse(
    String url, {
    bool blobData = false,
  });

  Future<dynamic> postResponse(
    String url, {
    required Map<String, dynamic> body,
    List<String> filePaths = const [],
    bool fromAuth = false,
    bool showError = true,
  });

  Future<dynamic> putResponse(
    String url, {
    required Map<String, dynamic> data,
    List<String> filePaths = const [],
    bool fromAuth = false,
  });

  Future<dynamic> deleteResponse(String url);
}
