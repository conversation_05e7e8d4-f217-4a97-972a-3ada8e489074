part of xr_helper;

class NotificationService {
  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    if (Firebase.apps.isEmpty) await Firebase.initializeApp();
  }

  static void init() async {
    final fcm = FirebaseMessaging.instance;

    await fcm.requestPermission(
      alert: true,
      badge: true,
      provisional: false,
      sound: true,
    );

    fcm.setForegroundNotificationPresentationOptions(
        badge: true, alert: true, sound: true);

    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    return;
  }

  //? Get Token
  static Future<String> getToken() async {
    final fcm = FirebaseMessaging.instance;
    if (kDebugMode) {
      return '';
    }

    final token = await fcm.getToken();

    Log.w('FCM_TOKEN: $token');

    return token ?? '';
  }

  //? Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    if (kDebugMode) {
      return;
    }

    final fcm = FirebaseMessaging.instance;

    Log.w('SUBSCRIBED_TO $topic');

    await fcm.subscribeToTopic(topic);
  }

  //? Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    final fcm = FirebaseMessaging.instance;

    Log.w('UNSUBSCRIBED FROM $topic');

    await fcm.unsubscribeFromTopic(topic);
  }

  static Future<void> sendNotification(
      {required String title,
      required String body,
      required String userTokenOrTopic,
      bool isTopic = false}) async {
    const firebaseProjectId = 'vip-barber-app';

    if (kDebugMode) {
      Log.w('Debug_SentNotification_To $userTokenOrTopic\n'
          'isTopic: $isTopic\n'
          'title: $title\n'
          'body: $body\n');
      return;
    }

    Log.w('SentNotificationTo $userTokenOrTopic');

    const String fcmUrl =
        'https://fcm.googleapis.com/v1/projects/$firebaseProjectId/messages:send';

    final accessToken = await AccessTokenFirebase().getAccessToken();

    final Map<String, dynamic> message = {
      'message': {
        'token': isTopic ? '/topics/$userTokenOrTopic' : userTokenOrTopic,
        'notification': {
          'title': title,
          'body': body,
        },
      }
    };

    final response = await http.post(
      Uri.parse(fcmUrl),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $accessToken',
      },
      body: jsonEncode(message),
    );

    if (response.statusCode == 200) {
      Log.w('NotificationSentSuccessfully ${response.body}');
    } else {
      Log.e('Failed to send notification: ${response.statusCode}');
      Log.e(response.body);
    }
  }
}

class AccessTokenFirebase {
  static const firebaseMessagingScope =
      'https://www.googleapis.com/auth/firebase.messaging';

  Future<String> getAccessToken() async {
    final jsonMap = **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;

    final client = await clientViaServiceAccount(
        ServiceAccountCredentials.fromJson(jsonMap), [firebaseMessagingScope]);

    final accessToken = client.credentials.accessToken.data;

    return accessToken;
  }
}
