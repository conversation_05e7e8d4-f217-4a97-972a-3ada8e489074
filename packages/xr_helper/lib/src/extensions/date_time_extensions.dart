part of xr_helper;

extension DateTimeExtentions on DateTime? {
  String get formatDateToString {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd', 'en').format(this!);
  }

  String get formatDateToStringWithTime {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd\nhh:mm:ss a', 'en').format(this!);
  }

  String get formatDateToStringWithTime2 {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd hh:mm a', 'en').format(this!);
  }

  String get formatTimeToString {
    if (this == null) return '';
    return DateFormat('hh:mm:ss a', 'en').format(this!);
  }

  String get formatTimeToStringWithoutPm {
    if (this == null) return '';
    return DateFormat('hh:mm', 'en').format(this!);
  }

  bool isToday() {
    if (this == null) return false;
    final now = DateTime.now();
    return now.year == this!.year &&
        now.month == this!.month &&
        now.day == this!.day;
  }
}
