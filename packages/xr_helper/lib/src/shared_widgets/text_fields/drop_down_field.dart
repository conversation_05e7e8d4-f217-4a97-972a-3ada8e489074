part of xr_helper;

class BaseDropDown extends StatelessWidget {
  final dynamic selectedValue;
  final String? label;
  final List<dynamic> data;
  final void Function(dynamic)? onChanged;
  final Widget? icon;
  final String Function(dynamic)? asString;
  final bool isRequired;
  final bool showTitle;
  final bool isWhiteText;

  const BaseDropDown(
      {super.key,
      required this.onChanged,
      this.asString,
      required this.data,
      required this.label,
      required this.selectedValue,
      this.isRequired = true,
      this.showTitle = true,
      this.isWhiteText = false,
      this.icon});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DropdownButtonFormField(
          value: selectedValue,
          dropdownColor: const Color(0xFF262626),
          isExpanded: true,
          borderRadius: BorderRadius.circular(AppRadius.radius8),
          items: data.map((e) {
            return DropdownMenuItem(
              value: e,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Text(asString != null ? asString!(e) : e.toString(),
                      style: isWhiteText
                          ? AppTextStyles.whiteLabelMedium
                          : AppTextStyles.labelLarge),
                ),
              ),
            );
          }).toList(),
          onChanged: onChanged,
          decoration: InputDecoration(
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.radius8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.radius8),
              borderSide: const BorderSide(color: Colors.white),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.radius8),
              borderSide: const BorderSide(color: Colors.white),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.radius8),
              borderSide: BorderSide(color: Colors.blueGrey.shade50),
            ),
            contentPadding: EdgeInsets.symmetric(
              vertical: AppSpaces.padding12,
              horizontal:
                  icon == null ? AppSpaces.padding12 : AppSpaces.padding8,
            ),
            label: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: AppSpaces.padding8),
              child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(label!,
                      style: isWhiteText
                          ? AppTextStyles.whiteLabelMedium
                          : AppTextStyles.labelMedium)),
            ),
            labelStyle:
                TextStyle(color: isWhiteText ? Colors.white : Colors.black),
            fillColor: Colors.transparent,
            prefixIcon: icon == null
                ? null
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: icon,
                  ),
          ),
          validator: (value) {
            if (value == null) {
              return 'برجاء اختيار $label';
            }
            return null;
          },
        ),
      ],
    );
  }
}
