part of xr_helper;

class AppTextStyles {
  static TextStyle headLine = const TextStyle(
    fontWeight: FontWeight.bold,
    fontSize: 28,
    color: Colors.white,
  );

  static TextStyle subHeadLine = const TextStyle(
    fontSize: 24,
    color: Colors.white,
    fontWeight: FontWeight.bold,
  );

  static TextStyle whiteSubHeadLine = const TextStyle(
    fontWeight: FontWeight.bold,
    fontSize: 24,
    color: Colors.white,
  );

  static TextStyle title = const TextStyle(
    color: Colors.white,
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  static TextStyle whiteTitle = const TextStyle(
    color: Colors.white,
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  static TextStyle subTitle = const TextStyle(
    color: Colors.white,
    fontSize: 16,
  );

  static TextStyle boldSubTitle = const TextStyle(
    color: Colors.white,
    fontWeight: FontWeight.bold,
    fontSize: 16,
  );

  static TextStyle whiteSubTitle = const TextStyle(
    color: Colors.white,
    fontSize: 16,
  );

  static TextStyle labelLarge = const TextStyle(
    color: Colors.white,
    fontSize: 14,
  );

  static TextStyle redLabelLarge = const TextStyle(
    color: Color(0xFFE74C3C),
    fontSize: 14,
  );

  static TextStyle greenLabelLarge = const TextStyle(
    color: Color(0xFF2ECC71),
    fontSize: 14,
  );

  static TextStyle whiteLabelLarge = const TextStyle(
    color: Colors.white,
    fontSize: 14,
  );

  static TextStyle labelMedium = const TextStyle(
    color: Colors.white,
    fontSize: 14,
  );

  static TextStyle whiteLabelMedium = const TextStyle(
    color: Colors.white,
    fontSize: 14,
  );

  static TextStyle labelSmall = const TextStyle(
    color: Colors.white,
  );

  static TextStyle body = const TextStyle(
    color: Colors.white,
  );

  static TextStyle whiteBody = const TextStyle(
    color: Colors.white,
  );

  static TextStyle hint = const TextStyle(
    color: Colors.white,
    fontSize: 16,
  );

  //! White hint
  static TextStyle whiteHint = const TextStyle(
    color: Colors.white,
    fontSize: 16,
  );

  //! Grey hint
  static TextStyle greyHint = const TextStyle(
    color: Colors.grey,
    fontSize: 16,
  );
}
