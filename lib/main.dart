import 'dart:io';

import 'package:barber_app/src/app.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'firebase_options.dart';

//? If click closest person button, navigate to choose map my radius 20 so closest one comes, navigate to provider confirmation screen
//? Providers remove times that have orders on time reservation
// * Minimum and max to pay in service
// * Pay all service cost or percentage cost
// * If home saved click navigate to lat lng
// * Make radius dynamic from API from start provider point
// * Calculate distance if free or have cost
// * Make service confirmation details
// * Make address container in provider details screen reservation tab
// * If schedule reservation clicked, Check if user is ok with time selected and navigate to map to check the available one, then navigate to provider confirmation screen

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await GetStorageService.init();

  HttpOverrides.global = MyHttpOverrides();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  NotificationService.init();

  runApp(const ProviderScope(child: BaseApp()));
}
