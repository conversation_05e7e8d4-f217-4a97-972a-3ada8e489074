// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAzo498Lt4oK70xtfkADPDYnvuA3FWbIMs',
    appId: '1:445266836146:web:6312ba393f30a90e2127ab',
    messagingSenderId: '445266836146',
    projectId: 'vip-barber-app',
    authDomain: 'vip-barber-app.firebaseapp.com',
    storageBucket: 'vip-barber-app.firebasestorage.app',
    measurementId: 'G-F6TEQY0PW1',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBfLeP3WZB7osHwbnu_Csq1UWUIfWURM4Q',
    appId: '1:445266836146:android:63b39845746f48032127ab',
    messagingSenderId: '445266836146',
    projectId: 'vip-barber-app',
    storageBucket: 'vip-barber-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDdiyuVE6ElSM3CxV7ncqZninlDGq85rSk',
    appId: '1:445266836146:ios:5a0a7b8b02bda2182127ab',
    messagingSenderId: '445266836146',
    projectId: 'vip-barber-app',
    storageBucket: 'vip-barber-app.firebasestorage.app',
    iosBundleId: 'com.ajory.barberApp',
  );
}
