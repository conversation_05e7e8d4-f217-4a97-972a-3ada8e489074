// import 'dart:developer';
//
// import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
// import 'package:barber_app/src/core/theme/color_manager.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class MapLocationPickerField extends HookWidget {
//   final ValueNotifier<LatLng?> selectedLocation;
//
//   const MapLocationPickerField({
//     super.key,
//     required this.selectedLocation,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () async {
//         await Navigator.push(
//           context,
//           MaterialPageRoute(
//             builder: (context) => _MapPicker(
//               locationNotifier: selectedLocation,
//             ),
//           ),
//         );
//       },
//       child: selectedLocation.value == null
//           ? InputDecorator(
//               decoration: InputDecoration(
//                 labelText: context.tr.pickLocation,
//                 border: const OutlineInputBorder(),
//               ),
//               child: Text(context.tr.tapToSelectLocation))
//           : ClipRRect(
//               borderRadius: BorderRadius.circular(AppRadius.radius12),
//               child: SizedBox(
//                 height: 200,
//                 width: double.infinity,
//                 child: _MapPicker(
//                   locationNotifier: selectedLocation,
//                   viewOnly: true,
//                 ),
//               ),
//             ),
//     );
//   }
// }
//
// GoogleMapController? _mapController;
//
// class _MapPicker extends HookWidget {
//   final ValueNotifier<LatLng?> locationNotifier;
//   final bool viewOnly;
//
//   const _MapPicker({
//     required this.locationNotifier,
//     this.viewOnly = false,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final markers = useState<Set<Marker>>({});
//     final mainLocation = useState<LatLng>(const LatLng(31.9522, 35.2332));
//
//     Future<bool> getPermission() async {
//       final geoLocationPermission = await Geolocator.checkPermission();
//
//       log('asfsafas ${geoLocationPermission.name}');
//
//       if (geoLocationPermission == LocationPermission.denied) {
//         final status = await Permission.location.request();
//         return status.isGranted;
//       } else {
//         return true;
//       }
//     }
//
//     Future<void> getCurrentLocation(
//       ValueNotifier<LatLng> mainLocation,
//       ValueNotifier<Set<Marker>> markers,
//     ) async {
//       Position position = await Geolocator.getCurrentPosition(
//           locationSettings:
//               const LocationSettings(accuracy: LocationAccuracy.medium));
//
//       LatLng currentLocation = LatLng(position.latitude, position.longitude);
//
//       _mapController?.animateCamera(
//         CameraUpdate.newCameraPosition(
//           CameraPosition(target: currentLocation, zoom: 15.0),
//         ),
//       );
//
//       mainLocation.value = currentLocation;
//       markers.value.clear();
//       markers.value.add(
//         Marker(
//           markerId: const MarkerId('currentLocation'),
//           position: mainLocation.value,
//         ),
//       );
//     }

//     Future<void> initializeLocation(
//       ValueNotifier<LatLng> mainLocation,
//       ValueNotifier<Set<Marker>> markers,
//     ) async {
//       final hasPermission = await getPermission();
//       if (hasPermission) {
//         await getCurrentLocation(mainLocation, markers);
//       } else {
//         final selectedPosition = locationNotifier.value;
//         if (selectedPosition != null) {
//           mainLocation.value = selectedPosition;
//           markers.value.add(
//             Marker(
//               markerId: const MarkerId('pickedLocation'),
//               position: mainLocation.value,
//             ),
//           );
//         }
//       }
//     }
//
//     useEffect(() {
//       if (viewOnly) {
//         //set the marker
//         markers.value.add(
//           Marker(
//             markerId: const MarkerId('pickedLocation'),
//             position: mainLocation.value,
//           ),
//         );
//       } else {
//         initializeLocation(mainLocation, markers);
//       }
//       return () {};
//     }, []);
//
//     return Scaffold(
//       body: Stack(
//         children: [
//           GoogleMap(
//             scrollGesturesEnabled: !viewOnly,
//             rotateGesturesEnabled: !viewOnly,
//             zoomGesturesEnabled: !viewOnly,
//             zoomControlsEnabled: false,
//             myLocationButtonEnabled: true,
//             myLocationEnabled: true,
//             initialCameraPosition: CameraPosition(
//               target: mainLocation.value,
//               zoom: 15,
//             ),
//             markers: markers.value,
//             onMapCreated: (controller) {
//               _mapController = controller;
//             },
//             onTap: (LatLng position) {
//               if (viewOnly) return;
//               mainLocation.value = position;
//               markers.value.clear();
//               markers.value.add(
//                 Marker(
//                   markerId: const MarkerId('pickedLocation'),
//                   position: mainLocation.value,
//                 ),
//               );
//             },
//           ),
//           if (viewOnly) ...[
//             Positioned(
//               top: 5,
//               right: 10,
//               child: TextButton(
//                 onPressed: () async {
//                   await Navigator.push(
//                     context,
//                     MaterialPageRoute(
//                       builder: (context) => _MapPicker(
//                         locationNotifier: locationNotifier,
//                       ),
//                     ),
//                   );
//                 },
//                 child: Text(context.tr.changeLocation,
//                     style: AppTextStyles.labelLarge.copyWith(
//                       fontWeight: FontWeight.bold,
//                       color: ColorManager.primaryColor,
//                       decoration: TextDecoration.underline,
//                       decorationColor: ColorManager.primaryColor,
//                     )),
//               ),
//             ),
//           ],
//           if (!viewOnly) ...[
//             Positioned(
//               top: 30,
//               right: 10,
//               child: FloatingActionButton(
//                 onPressed: () => getCurrentLocation(mainLocation, markers),
//                 child: const Icon(Icons.my_location),
//               ),
//             ),
//             Positioned(
//               top: 30,
//               left: 10,
//               child: FloatingActionButton(
//                 backgroundColor: Colors.white,
//                 onPressed: () => Navigator.of(context).pop(),
//                 child: const Icon(
//                   CupertinoIcons.back,
//                   size: 30,
//                   color: ColorManager.primaryColor,
//                 ),
//               ),
//             ),
//             Positioned(
//               bottom: 20,
//               left: 20,
//               right: 20,
//               child: Button(
//                 onPressed: () {
//                   locationNotifier.value = mainLocation.value;
//                   Navigator.of(context).pop();
//                 },
//                 label: context.tr.save,
//               ),
//             ),
//           ],
//         ],
//       ),
//     );
//   }
// }
