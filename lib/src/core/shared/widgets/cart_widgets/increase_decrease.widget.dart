import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class IncreaseDecreaseWidget extends StatelessWidget {
  final ValueNotifier<int> quantity;
  final Function(int)? onChange;
  final bool haveTopPadding;

  const IncreaseDecreaseWidget({
    super.key,
    required this.quantity,
    this.haveTopPadding = false,
    this.onChange,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        IconButton(
          onPressed: () {
            quantity.value++;
            if (onChange != null) {
              onChange!(quantity.value);
            }
          },
          icon: const Icon(
            Icons.add_circle_outlined,
            color: ColorManager.primaryColor,
          ),
        ),
        ValueListenableBuilder<int>(
          valueListenable: quantity,
          builder: (context, value, child) {
            return Text(
              value.toString(),
              style: AppTextStyles.subTitle,
            );
          },
        ).paddingOnly(top: haveTopPadding ? AppSpaces.padding8 : 0),
        IconButton(
          onPressed: () {
            if (quantity.value > 1) {
              quantity.value--;
              if (onChange != null) {
                onChange!(quantity.value);
              }
            }
          },
          icon: const Icon(
            Icons.remove_circle_outline,
            color: ColorManager.primaryColor,
          ),
        ),
      ],
    );
  }
}
