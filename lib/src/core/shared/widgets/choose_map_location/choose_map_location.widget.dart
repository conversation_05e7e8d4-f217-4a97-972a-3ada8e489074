import 'package:barber_app/src/core/consts/app_constants.dart';
import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../screens/auth/models/user_model.dart';
import 'mixins/map.mixin.dart';

class ChooseMapLocationWidget extends HookConsumerWidget with MapMixin {
  final ValueNotifier<LatLng?> selectedLocation;
  final LatLng? startLocation;
  final num? locationRadiusInKM;
  final void Function(LatLng? userPickedLocation)? onSaved;

  const ChooseMapLocationWidget({
    super.key,
    this.startLocation,
    this.locationRadiusInKM,
    this.onSaved,
    required this.selectedLocation,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final markers = useState<Set<Marker>>({});
    final initUserLocation = UserModel.currentUser.latLng;
    final isOutsideArea = useState<bool>(false);

    final mainLocation =
        useState<LatLng>(initUserLocation ?? AppConsts.defaultLocation);

    final myMapController = useState<GoogleMapController?>(null);

    Future<bool> getPermission() async {
      try {
        final geoLocationPermission = await Geolocator.checkPermission();

        if (geoLocationPermission == LocationPermission.denied) {
          final status = await Permission.location.request();
          return status.isGranted;
        } else {
          return true;
        }
      } catch (e, s) {
        Log.e('Error while getting permission $e $s');
        return false;
      }
    }

    Future<void> getCurrentLocation() async {
      Position position = await Geolocator.getCurrentPosition(
          locationSettings:
              const LocationSettings(accuracy: LocationAccuracy.medium));

      LatLng currentLocation = LatLng(position.latitude, position.longitude);

      myMapController.value?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(target: currentLocation, zoom: 15.0),
        ),
      );

      mainLocation.value = currentLocation;
      // markers.value.clear();
      // markers.value.add(
      //   Marker(
      //     markerId: const MarkerId('currentLocation'),
      //     position: mainLocation.value,
      //   ),
      // );
    }

    initData() async {
      await getPermission();

      // if (initLocation != null) {
      //   mainLocation.value = initLocation!;
      //
      //   myMapController.value!.animateCamera(
      //     CameraUpdate.newCameraPosition(
      //       CameraPosition(
      //         target: mainLocation.value,
      //         zoom: 15.0,
      //       ),
      //     ),
      //   );
      // } else {
      await getCurrentLocation();
      // }
    }

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        initData();
      });

      return () {};
    }, []);

    bool checkIfOutsideArea(LatLng position) {
      if (startLocation == null || locationRadiusInKM == null) return false;

      final distance = Geolocator.distanceBetween(
            startLocation!.latitude,
            startLocation!.longitude,
            position.latitude,
            position.longitude,
          ) /
          1000; // Convert meters to kilometers

      return distance > locationRadiusInKM!;
    }

    return Column(
      children: [
        Expanded(
          child: Stack(
            children: [
              GoogleMap(
                initialCameraPosition: CameraPosition(
                  target: mainLocation.value,
                  zoom: 15.0,
                ),
                markers: markers.value,
                mapType: MapType.normal,
                circles: {
                  if (startLocation != null && locationRadiusInKM != null)
                    Circle(
                      circleId: const CircleId('area'),
                      center: startLocation!,
                      radius: locationRadiusInKM! * 1000,
                      // Convert kilometers to meters
                      fillColor: Colors.blue.withOpacity(0.1),
                      strokeColor: Colors.blue,
                      strokeWidth: 1,
                    ),
                },
                myLocationEnabled: true,
                myLocationButtonEnabled: true,
                onMapCreated: (controller) {
                  myMapController.value = controller;
                },
                onCameraMove: (position) {
                  selectedLocation.value = position.target;
                  isOutsideArea.value = checkIfOutsideArea(position.target);
                },
              ),
              const Center(
                child: Icon(
                  Icons.location_pin,
                  color: Colors.red,
                  size: 40,
                ),
              ),
              if (isOutsideArea.value)
                Positioned(
                  bottom: 20,
                  left: 20,
                  right: 20,
                  child: Container(
                    padding: const EdgeInsets.all(AppSpaces.padding16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                      color: Colors.red,
                    ),
                    child: Text(
                      context.tr.youAreOutSideTheAvailableArea,
                      style: const TextStyle(color: Colors.white),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ),
        AppGaps.gap24,
        Padding(
          padding: const EdgeInsets.all(
            AppSpaces.screenPadding,
          ),
          child: Button(
            label: context.tr.save,
            textColor: Colors.black,
            isPrefixIcon: true,
            onPressed: () async {
              if (isOutsideArea.value) {
                showToast(context.tr.youAreOutSideTheAvailableArea,
                    isError: true);
                return;
              }

              final currentUser = UserModel.currentUser;

              final copiedUser = currentUser.copyWith(
                latLng: selectedLocation.value,
              );

              await GetStorageService.setData(
                key: LocalKeys.user,
                value: copiedUser.toJson(),
              );

              if (onSaved != null) onSaved!(selectedLocation.value);

              showToast(context.tr.savedSuccessfully);
            },
          ),
        ),
      ],
    );
  }
}

// import 'package:barber_app/src/core/consts/app_constants.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
//
// import '../../../../screens/auth/models/user_model.dart';
// import 'mixins/map.mixin.dart';
//
// class ChooseMapLocationWidget extends HookConsumerWidget with MapMixin {
//   final LatLng? initLocation;
//   final ValueNotifier<LatLng?> selectedLocation;
//   final LatLng? startLocation;
//   final num? locationRadiusInKM;
//
//   const ChooseMapLocationWidget({
//     super.key,
//     this.initLocation,
//     this.startLocation,
//     this.locationRadiusInKM,
//     required this.selectedLocation,
//   });
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final markers = useState<Set<Marker>>({});
//     final initUserLocation = UserModel.currentUser.latLng;
//
//     final mainLocation = useState<LatLng>(
//         initLocation ?? initUserLocation ?? AppConsts.defaultLocation);
//
//     final myMapController = useState<GoogleMapController?>(null);
//
//     initData() async {
//       if (initLocation != null) {
//         mainLocation.value = initLocation!;
//
//         myMapController.value!.animateCamera(
//           CameraUpdate.newCameraPosition(
//             CameraPosition(
//               target: mainLocation.value,
//               zoom: 15.0,
//             ),
//           ),
//         );
//       }
//     }
//
//     useEffect(() {
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         initData();
//       });
//
//       return () {};
//     }, []);
//
//     return Stack(
//       children: [
//         GoogleMap(
//           initialCameraPosition: CameraPosition(
//             target: mainLocation.value,
//             zoom: 15.0,
//           ),
//           // polylines: {},
//           markers: markers.value,
//           mapType: MapType.normal,
//           onMapCreated: (controller) {
//             myMapController.value = controller;
//           },
//           onCameraMove: (position) {
//             selectedLocation.value = position.target;
//           },
//         ),
//         const Center(
//           child: Icon(
//             Icons.location_pin,
//             color: Colors.red,
//             size: 40,
//           ),
//         ),
//       ],
//     );
//   }
// }
