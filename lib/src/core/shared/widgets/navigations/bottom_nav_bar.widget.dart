import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../theme/color_manager.dart';
import 'controller/bottom_nav_bar.controller.dart';

class BottomNavBarWidget extends ConsumerWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);

    final iconSize = 20.w;

    final index0 = currentIndex == 0;
    final index1 = currentIndex == 1;
    final index2 = currentIndex == 2;
    final index3 = currentIndex == 3;

    return ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(25.w),
        topRight: Radius.circular(25.w),
      ),
      child: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        unselectedItemColor: ColorManager.white,
        selectedItemColor: ColorManager.primaryColor,
        selectedLabelStyle: AppTextStyles.labelMedium.copyWith(fontSize: 12),
        unselectedLabelStyle: AppTextStyles.labelLarge.copyWith(
          color: ColorManager.white,
          fontSize: 12,
        ),
        elevation: 1,
        backgroundColor: ColorManager.black,
        currentIndex: currentIndex,
        onTap: (index) {
          if (!UserModel.isLogged() && (index == 2 || index == 3)) {
            showToast(context.tr.loginFirstPlease, isError: true);
            return;
          }
          bottomNavCtrl.changeIndex(index);
        },
        items: [
          BottomNavigationBarItem(
            icon: Padding(
              padding: const EdgeInsets.all(AppSpaces.padding4),
              child: 'assets/icons/home.png'.assetImage(
                width: iconSize,
                color: index0 ? ColorManager.primaryColor : ColorManager.white,
              ),
            ),
            // Icon(
            //   CupertinoIcons.house_fill,
            //   size: iconSize,
            //   color: index0 ? ColorManager.primaryColor : ColorManager.white,
            // ),
            label: context.tr.home,
          ),
          BottomNavigationBarItem(
            icon: Padding(
              padding: const EdgeInsets.all(AppSpaces.padding4),
              child: 'assets/icons/cart.png'.assetImage(
                width: iconSize,
                color: index1 ? ColorManager.primaryColor : ColorManager.white,
              ),
            ),
            label: context.tr.cart,
          ),
          BottomNavigationBarItem(
            icon: 'assets/icons/orders.png'.assetImage(
              width: iconSize,
              color: index2 ? ColorManager.primaryColor : ColorManager.white,
            ),
            label: context.tr.orders,
          ),
          BottomNavigationBarItem(
            icon: Padding(
              padding: const EdgeInsets.all(AppSpaces.padding4),
              child: 'assets/icons/user.png'.assetImage(
                width: iconSize,
                color: index3 ? ColorManager.primaryColor : ColorManager.white,
              ),
            ),
            label: context.tr.profile,
          ),
        ],
      ),
    );
  }
}
