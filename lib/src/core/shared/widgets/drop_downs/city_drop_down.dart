import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:barber_app/src/screens/auth/models/city_model.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../screens/auth/providers/auth_providers.dart';

class CityDropDown extends ConsumerWidget {
  final ValueNotifier<CityModel?> selectedCity;
  final String? selectedCityName;

  const CityDropDown(
      {super.key, required this.selectedCity, this.selectedCityName});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getCitiesFuture = ref.watch(getCitiesFutureProvider);

    return getCitiesFuture.get(
      data: (cities) {
        return HookBuilder(builder: (context) {
          if (selectedCity.value == null && selectedCityName != null) {
            // widget time stamp
            WidgetsBinding.instance.addPostFrameCallback((_) {
              selectedCity.value = cities.firstWhereOrNull(
                (city) => city.name == selectedCityName,
              );
            });
          }

          return BaseDropDown(
            label: context.tr.city,
            isWhiteText: true,
            selectedValue: selectedCity.value,
            data: cities,
            onChanged: (value) {
              selectedCity.value = value;
            },
            asString: (city) => (city as CityModel).name,
          );
        });
      },
    );
  }
}
