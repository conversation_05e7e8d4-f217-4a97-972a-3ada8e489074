import 'package:flutter/material.dart';
import 'package:shimmer_animation/shimmer_animation.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../theme/color_manager.dart';

class BaseShimmerLoading extends StatelessWidget {
  final Widget child;
  final double radius;
  const BaseShimmerLoading(
      {super.key, required this.child, this.radius = AppRadius.radius12});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(radius),
      child: Shimmer(
        color: ColorManager.lightGrey,
        child: child,
      ),
    );
  }
}
