import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

import '../../../theme/color_manager.dart';

class LoadingWidget extends StatelessWidget {
  final bool isLinear;

  const LoadingWidget({super.key, this.isLinear = false});

  @override
  Widget build(BuildContext context) {
    if (isLinear) {
      return LinearProgressIndicator(
        backgroundColor: ColorManager.primaryColor.withOpacity(0.2),
        valueColor:
            const AlwaysStoppedAnimation<Color>(ColorManager.primaryColor),
      );
    }
    return Center(
      child: LoadingAnimationWidget.discreteCircle(
          color: ColorManager.primaryColor, size: 45),
    );
  }
}
