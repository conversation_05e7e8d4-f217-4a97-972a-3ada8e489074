// import 'package:circular_countdown_timer/circular_countdown_timer.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:helpers/src/features/auth/controllers/relations.controller.dart';
//
// class TimerWidget extends ConsumerWidget {
//   const TimerWidget({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final auth = ref.watch(authControllerProvider(context));
//
//     final CountDownController timerController = CountDownController();
//
//     bool _disabledTimer = false;
//
//     bool get disabledTimer => _disabledTimer;
//
//     set disabledTimer(bool value) {
//       _disabledTimer = value;
//       notifyListeners();
//     }
//
//     return CircularCountDownTimer(
//       duration: 120,
//       initialDuration: 0,
//       controller: auth.timerController,
//       width: 40,
//       height: 40,
//       ringColor: Colors.transparent,
//       fillColor: Colors.transparent,
//       textStyle:
//           const TextStyle(color: Colors.blue, fontWeight: FontWeight.bold),
//       textFormat: CountdownTextFormat.MM_SS,
//       isReverse: true,
//       isReverseAnimation: true,
//       isTimerTextShown: auth.disabledTimer,
//       autoStart: false,
//       onStart: () {
//         auth.disabledTimer = true;
//       },
//       onComplete: () {
//         auth.disabledTimer = false;
//       },
//     );
//   }
// }
