import 'dart:async';

import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../screens/auth/controllers/verify_otp.controller.dart';
import '../../../../theme/color_manager.dart';
import '../../loading/loading.widget.dart';

StreamController<ErrorAnimationType>? _errorController;

final pinTheme = PinTheme(
    shape: PinCodeFieldShape.box,
    borderRadius: BorderRadius.circular(5),
    fieldHeight: 50,
    fieldWidth: 40,
    activeFillColor: Colors.white,
    inactiveFillColor: Colors.white,
    selectedFillColor: Colors.white,
    inactiveColor: ColorManager.secondaryColor,
    activeColor: ColorManager.secondaryColor);

class OtpField extends HookConsumerWidget {
  final ValueNotifier<bool> phoneVerified;
  final Function(String? otp)? onSuccess;
  final bool shouldValidatePhone;

  const OtpField({
    super.key,
    required this.phoneVerified,
    this.onSuccess,
    required this.shouldValidatePhone,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final verifyOTPController = ref.watch(verifyOTPControllerProvider);
    final _focusNode = useFocusNode();

    void onCompleted(String verificationCode) async {
      if (shouldValidatePhone) {
        final isPhoneOTPVerified = await verifyOTPController.verifyOTP(
          otp: verificationCode,
        );

        phoneVerified.value = isPhoneOTPVerified;
      } else {
        phoneVerified.value = true;
      }

      if (!context.mounted) return;

      if (phoneVerified.value) {
        if (onSuccess != null) {
          onSuccess!(verificationCode);
        } else {
          context.back();
          showToast(context.tr.verificationSuccessful);
        }
      } else {
        showToast(context.tr.verificationCodeIsWrong, isError: true);
      }
    }

    return verifyOTPController.isLoading
        ? const LoadingWidget()
        : Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 30),
            child: Directionality(
              textDirection: TextDirection.ltr,
              child: PinCodeTextField(
                appContext: context,
                focusNode: _focusNode,
                animationType: AnimationType.fade,
                length: 6,
                validator: (v) {
                  if (v!.length < 5) {
                    return context.tr.completeVerification;
                  } else {
                    return null;
                  }
                },
                pinTheme: pinTheme,
                cursorColor: Colors.black,
                animationDuration: const Duration(milliseconds: 300),
                enableActiveFill: true,
                errorAnimationController: _errorController,
                keyboardType: TextInputType.number,
                boxShadows: const [
                  BoxShadow(
                    offset: Offset(0, 1),
                    color: Colors.black12,
                    blurRadius: 10,
                  )
                ],
                onCompleted: onCompleted,
                // onChanged: (String value) {
                //   otpNotifier.value = value;
                // },
              ),
            ),
            // OtpTextField(
            //   textStyle: AppTextStyles.labelLarge,
            //   numberOfFields: 6,
            //   borderColor: ColorManager.secondaryColor,
            //   showFieldAsBox: true,
            //   onCodeChanged: (String code) {},
            //   onSubmit: onCompleted,
            // ),
          );
  }
}
