import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../theme/color_manager.dart';

class BaseTabBarWidget extends HookWidget {
  final List<String> tabs;
  final ValueNotifier<int> selectedTab;

  const BaseTabBarWidget({
    super.key,
    required this.tabs,
    required this.selectedTab,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(AppSpaces.padding8),
        child: Wrap(
          // mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(tabs.length, (index) {
            final isSelected = selectedTab.value == index;
            return GestureDetector(
              onTap: () {
                selectedTab.value = index;
              },
              child: Container(
                margin: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.padding8,
                    vertical: AppSpaces.padding8),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpaces.padding24,
                  vertical: AppSpaces.padding12,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? ColorManager.primaryColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(AppRadius.radius100),
                  border: Border.all(
                    color: isSelected ? Colors.transparent : Colors.white,
                    width: 2,
                  ),
                ),
                child: Text(
                  tabs[index],
                  style: TextStyle(
                    color: isSelected ? Colors.black : Colors.white,
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }
}
