import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String? title;
  final bool showBack;

  const BaseAppBar({
    super.key,
    this.title,
    this.showBack = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      automaticallyImplyLeading: false,
      backgroundColor: Colors.transparent,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      centerTitle: true,
      leading: showBack
          ? IconButton(
              onPressed: () => navService.back(),
              icon: const Icon(
                Icons.arrow_back_ios,
                color: Colors.white,
              ),
            )
          : null,
      title: title == null
          ? null
          : Text(
              title!,
              style: AppTextStyles.title,
            ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
