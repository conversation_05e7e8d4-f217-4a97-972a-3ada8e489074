import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:flutter/cupertino.dart';
import 'package:xr_helper/xr_helper.dart';

class EmptyDataWidget extends StatelessWidget {
  const EmptyDataWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          const Icon(
            CupertinoIcons.info,
            size: 100,
            color: ColorManager.grey,
          ),
          AppGaps.gap12,
          Text(
            context.tr.noDataFound,
            style: AppTextStyles.title,
          ),
        ],
      ),
    );
  }
}
