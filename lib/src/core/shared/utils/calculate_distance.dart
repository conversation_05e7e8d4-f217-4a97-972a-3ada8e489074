import 'package:geolocator/geolocator.dart' show Geolocator;
import 'package:google_maps_flutter/google_maps_flutter.dart' show LatLng;

double calculateDistance(LatLng? start, LatLng? end) {
  if (start == null || end == null) return 0;

  return Geolocator.distanceBetween(
        start.latitude,
        start.longitude,
        end.latitude,
        end.longitude,
      ) /
      1000; // Convert meters to kilometers
}
