import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

void onUserLogged(BuildContext context, {required Function onLogged}) {
  if (!UserModel.isLogged()) {
    showToast(
      context.tr.loginFirstPlease,
      isError: true,
    );

    return;
  }

  onLogged();
}
