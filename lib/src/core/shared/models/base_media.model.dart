import 'package:barber_app/src/core/consts/network/api_endpoints.dart';

import 'base_model.dart';

class BaseMediaModel extends BaseModel {
  final String url;
  final String? previewUrl;

  const BaseMediaModel({
    super.id,
    super.documentId,
    super.createdAt,
    this.url = '',
    this.previewUrl,
  });

  factory BaseMediaModel.fromJson(Map<String, dynamic> json) {
    return BaseMediaModel(
      id: json['id'] ?? 0,
      documentId: json['documentId'] ?? '',
      url: json['url'] != null
          ? json['url'].toString().startsWith('/uploads')
              ? '${ApiEndpoints.url}${json['url']}'
              : json['url']
          : '',
      previewUrl: json['previewUrl'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  // to json
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'documentId': documentId,
      'url': url,
      'previewUrl': previewUrl,
      'createdAt': createdAt?.toIso8601String(),
    };
  }

  static BaseMediaModel empty() {
    return BaseMediaModel(
      id: 0,
      url: '',
      previewUrl: '',
      createdAt: DateTime.now(),
    );
  }
}
