import 'package:barber_app/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:barber_app/src/screens/home/<USER>/home.screen.dart';
import 'package:barber_app/src/screens/order/view/order.screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import '../../auth/view/profile_screen/profile.screen.dart';
import '../../cart/view/cart_screen/cart.screen.dart';

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(
      bottomNavigationControllerProvider,
    );

    return Scaffold(
      body: _SelectedScreen(currentIndex: currentIndex),
      bottomNavigationBar: const BottomNavBarWidget(),
    );
  }
}

class _SelectedScreen extends StatelessWidget {
  final int currentIndex;

  const _SelectedScreen({required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    switch (currentIndex) {
      case 0:
        return const HomeScreen();
      case 1:
        return const CartScreen();
      case 2:
        return const OrdersScreen();

      case 3:
        return const ProfileScreen();
    }
    return const SizedBox.shrink();
  }
}
