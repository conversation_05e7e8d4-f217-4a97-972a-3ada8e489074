import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/auth_controller.dart';
import '../repositories/auth_repository.dart';

// * Auth Repo Provider ========================================
final authRepoProvider = Provider<AuthRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return AuthRepository(networkApiService: networkApiService);
});

// * Auth Change Notifier Provider ========================================
final authControllerNotifierProvider = ChangeNotifierProvider<AuthController>(
  (ref) {
    final authRepo = ref.watch(authRepoProvider);

    return AuthController(
      authRepo: authRepo,
    );
  },
);

// * Auth Provider ========================================
final authControllerProvider = Provider<AuthController>(
  (ref) {
    final authRepo = ref.watch(authRepoProvider);

    return AuthController(
      authRepo: authRepo,
    );
  },
);

// * Get Cities Future Provider ========================================
final getCitiesFutureProvider = FutureProvider(
  (
    ref,
  ) {
    final authController = ref.watch(authControllerProvider);

    return authController.getCities();
  },
);

// * Get Providers
final getProvidersFutureProvider = FutureProvider(
  (
    ref,
  ) {
    final authController = ref.watch(authControllerProvider);

    return authController.getProviders();
  },
);

// * Get Providers By Service
final getProvidersByCityAndServiceFutureProvider = FutureProvider.family(
  (
    ref,
    String? serviceDocId,
  ) {
    final authController = ref.watch(authControllerProvider);

    return authController.getProvidersByCityAndService(
      serviceDocId: serviceDocId,
    );
  },
);
