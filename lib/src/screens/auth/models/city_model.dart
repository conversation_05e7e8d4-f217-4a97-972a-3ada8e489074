import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class CityModel extends Equatable {
  final int? id;
  final String name;
  final num deliveryTime;
  final num deliveryCost;
  final num? lat;
  final num? long;
  final int radius;

  const CityModel({
    this.id,
    this.name = '',
    this.deliveryTime = 0,
    this.deliveryCost = 0,
    this.lat,
    this.long,
    this.radius = 0,
  });

  LatLng? get latLng {
    if (lat == null || long == null) return null;
    return LatLng(lat!.toDouble(), long!.toDouble());
  }

  // * From Json
  factory CityModel.fromJson(Map<String, dynamic> json) {
    return CityModel(
      id: json['id'],
      name: json['name'] ?? '',
      deliveryTime: json['delivery_time'] ?? 0,
      deliveryCost: json['delivery_cost'] ?? 0,
      lat: num.tryParse(json['lat']?.toString() ?? ''),
      long: num.tryParse(json['long']?.toString() ?? ''),
      radius: json['radius'] ?? 0,
    );
  }

  // * To Json
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'delivery_time': deliveryTime,
      'delivery_cost': deliveryCost,
      'lat': lat,
      'long': long,
      'radius': radius,
    };
  }

  factory CityModel.empty() => const CityModel();

  @override
  List<Object?> get props => [
        id,
        name,
        deliveryTime,
        deliveryCost,
        lat,
        long,
        radius,
      ];
}
