import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class ChooseGenderWidget extends HookWidget {
  final ValueNotifier<UserGender> gender;

  const ChooseGenderWidget({super.key, required this.gender});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
            child: TextButton(
          onPressed: () {
            gender.value = UserGender.male;
          },
          child: Container(
            alignment: Alignment.center,
            width: double.infinity,
            padding: const EdgeInsets.all(AppSpaces.padding12),
            decoration: BoxDecoration(
              borderRadius:
                  const BorderRadius.all(Radius.circular(AppRadius.radius100)),
              color: gender.value == UserGender.male
                  ? ColorManager.primaryColor
                  : Colors.transparent,
              border: Border.all(
                  color: gender.value == UserGender.male
                      ? Colors.transparent
                      : ColorManager.secondaryColor),
            ),
            child: Text(
              context.tr.male,
              style: gender.value == UserGender.male
                  ? AppTextStyles.labelLarge.copyWith(color: Colors.black)
                  : AppTextStyles.whiteLabelLarge,
            ),
          ),
        )),
        Expanded(
            child: TextButton(
          onPressed: () {
            gender.value = UserGender.female;
          },
          child: Container(
            alignment: Alignment.center,
            width: double.infinity,
            padding: const EdgeInsets.all(AppSpaces.padding12),
            decoration: BoxDecoration(
              borderRadius:
                  const BorderRadius.all(Radius.circular(AppRadius.radius100)),
              color: gender.value == UserGender.female
                  ? ColorManager.primaryColor
                  : Colors.transparent,
              border: Border.all(
                  color: gender.value == UserGender.female
                      ? Colors.transparent
                      : ColorManager.secondaryColor),
            ),
            child: Text(
              context.tr.female,
              style: gender.value == UserGender.female
                  ? AppTextStyles.labelLarge.copyWith(color: Colors.black)
                  : AppTextStyles.whiteLabelLarge,
            ),
          ),
        )),
      ],
    );
  }
}
