import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/screens/auth/view/profile_screen/widgets/update_profile_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/shared/widgets/app_bar/base_appbar.dart';

class UpdateProfileScreen extends HookConsumerWidget {
  const UpdateProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    return Scaffold(
      appBar: BaseAppBar(
        title: context.tr.updateProfile,
      ),
      body: FormBuilder(
        key: form<PERSON><PERSON>,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: <PERSON>View(
            children: [
              // * Fields Container
              UpdateProfileFieldsWidget(
                formKey: formKey,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
