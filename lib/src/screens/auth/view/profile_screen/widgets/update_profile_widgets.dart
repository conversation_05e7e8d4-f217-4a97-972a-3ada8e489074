import 'dart:developer';

import 'package:barber_app/src/core/consts/network/api_endpoints.dart';
import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/consts/network/api_strings.dart';
import '../../../../../core/shared/widgets/drop_downs/city_drop_down.dart';
import '../../../../../core/shared/widgets/fields/text_field.dart';
import '../../../../../core/shared/widgets/verify_bottom_sheet/verify_code_bottom_sheet.dart';
import '../../../../../core/theme/color_manager.dart';
import '../../../../main_screen/view/main.screen.dart';
import '../../../controllers/verify_otp.controller.dart';
import '../../../models/city_model.dart';
import '../../../models/user_model.dart';
import '../../../providers/auth_providers.dart';
import '../../register/widgets/choose_gender.widget.dart';

class UpdateProfileFieldsWidget extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const UpdateProfileFieldsWidget({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerProvider);
    final verifyOTPController = ref.watch(verifyOTPControllerProvider);

    final phoneController = useTextEditingController(
        text: filteredPhone(UserModel.currentUser.phone,
            withOutCountryCode: true));

    final valueNotifiers = {
      FieldsConsts.city: useState<CityModel?>(null),
      FieldsConsts.gender:
          useState<UserGender>(UserModel.currentUser.gender ?? UserGender.male),
      FieldsConsts.isPhoneVerified: useState(false),
      FieldsConsts.originalPhone: useState(UserModel.currentUser.phone),
    };

    final city =
        valueNotifiers[FieldsConsts.city]! as ValueNotifier<CityModel?>;
    final gender =
        valueNotifiers[FieldsConsts.gender]! as ValueNotifier<UserGender>;
    final isPhoneVerified =
        valueNotifiers[FieldsConsts.isPhoneVerified]! as ValueNotifier<bool>;
    final originalPhone =
        valueNotifiers[FieldsConsts.originalPhone]! as ValueNotifier<String>;

    bool hasPhoneChanged() {
      return phoneController.text !=
          filteredPhone(originalPhone.value, withOutCountryCode: true);
    }

    // Extract profile update logic into a separate method
    Future<void> _updateProfile() async {
      final formData = formKey.currentState?.value;

      try {
        log('afqsfsaf ${formData}');

        await authController.updateProfile(
          data: formData!,
          city: city.value,
        );

        const MainScreen().navigateReplacement;

        showToast(
          context.tr.profileUpdatedSuccessfully,
        );
      } catch (e) {
        showToast(e.toString(), isError: true);
      }
    }

    void showVerifyBottomSheet() async {
      try {
        final user = await authController.getUserByPhone(
            filteredPhone(phoneController.text, withOutCountryCode: true),
            saveUserToLocal: false);

        if (user != UserModel.empty()) {
          showToast(context.tr.mobileNumberAlreadyTaken, isError: true);
          return;
        }

        await verifyOTPController.signInWithPhoneNumber(
            phone: phoneController.text);

        showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppRadius.radius12),
                    topRight: Radius.circular(AppRadius.radius12))),
            builder: (ctx) => VerifyOtpBottomSheet(
                  onSuccess: (otp) async {
                    isPhoneVerified.value = true;
                    // Store the new phone in local storage for comparison
                    GetStorageService.setData(
                      key: LocalKeys.currentUserPhone,
                      value: phoneController.text,
                    );

                    // Close the bottom sheet
                    Navigator.pop(context);

                    // Proceed with profile update after successful verification
                    await _updateProfile();
                  },
                  phoneNumber: phoneController.text,
                  shouldValidatePhone: true,
                  phoneVerified: isPhoneVerified,
                ));
      } catch (e) {
        if (e.toString().contains('Daily OTP limit reached')) {
          showToast(context.tr.otpLimitReached, isError: true);
        } else {
          showToast(e.toString(), isError: true);
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        //! Full Name ------------------------------
        BaseTextField(
          name: FieldsConsts.name,
          title: context.tr.fullName,
          initialValue: UserModel.currentUser.name,
        ),

        AppGaps.gap12,

        //! Address ------------------------------
        BaseTextField(
          name: FieldsConsts.address,
          title: context.tr.address,
          initialValue: UserModel.currentUser.address,
        ),

        AppGaps.gap24,

        //! City
        CityDropDown(
          selectedCity: city,
          selectedCityName: UserModel.currentUser.city?.name,
        ),

        AppGaps.gap12,

        //! Mobile Number ------------------------------
        BaseTextField(
          name: FieldsConsts.phone,
          controller: phoneController,
          icon: isPhoneVerified.value
              ? const Icon(
                  Icons.check_circle,
                  color: ColorManager.successColor,
                )
              : hasPhoneChanged()
                  ? IconButton(
                      icon: const Icon(Icons.verified_user),
                      onPressed: showVerifyBottomSheet,
                    )
                  : null,
          hint: '0534576556',
          title: context.tr.mobileNumber,
          textAlign: TextAlign.left,
          withoutEnter: true,
        ),

        AppGaps.gap12,

        //! Password ------------------------------
        // BaseTextField(
        //   name: FieldsConsts.password,
        //   title: context.tr.password,
        //   controller: passwordController,
        //   hint: '********',
        //   textInputType: TextInputType.visiblePassword,
        //   isObscure: true,
        //   withoutEnter: true,
        //   isRequired: false,
        // ),

        // AppGaps.gap12,

        //! Gender ------------------------------
        ChooseGenderWidget(
          gender: gender,
        ),

        AppGaps.gap24,

        Button(
          isWhiteText: false,
          isLoading: authController.isLoading,
          onPressed: () async {
            if (formKey.currentState?.saveAndValidate() ?? false) {
              // Check if phone changed and needs verification
              if (hasPhoneChanged() && !isPhoneVerified.value) {
                // Show OTP verification sheet instead of toast
                showVerifyBottomSheet();
                return;
              }

              // If phone not changed or already verified, proceed with update
              await _updateProfile();
            }
          },
          label: context.tr.save,
        ),
      ],
    );
  }
}
