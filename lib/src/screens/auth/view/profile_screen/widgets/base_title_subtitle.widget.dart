import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/theme/color_manager.dart';

class BaseTitleSubtitleWidget extends StatelessWidget {
  final String title;
  final String? subtitle;
  final TextDirection? textDirection;
  final Widget? icon;
  final void Function()? onTap;

  const BaseTitleSubtitleWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.textDirection,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(
          top: AppSpaces.padding16,
        ),
        padding: const EdgeInsets.all(
          AppSpaces.padding16,
        ),
        decoration: BoxDecoration(
          color: ColorManager.black,
          borderRadius: BorderRadius.circular(AppRadius.radius12),
        ),
        child: Row(
          children: [
            if (icon != null) ...[
              icon!,
              AppGaps.gap8,
            ],
            Text(
              title,
              style: AppTextStyles.whiteLabelLarge,
            ),
            if (subtitle != null) ...[
              AppGaps.gap4,
              Text(
                subtitle!,
                style: AppTextStyles.whiteLabelLarge.copyWith(
                  color: Colors.blueGrey.shade50,
                ),
                textDirection: textDirection,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
