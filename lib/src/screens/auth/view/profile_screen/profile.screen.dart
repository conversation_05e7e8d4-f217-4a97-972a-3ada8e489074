import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:barber_app/src/core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import 'package:barber_app/src/screens/auth/view/profile_screen/update_profile.screen.dart';
import 'package:barber_app/src/screens/auth/view/profile_screen/widgets/base_title_subtitle.widget.dart';
import 'package:barber_app/src/screens/setting/providers/setting_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/theme/color_manager.dart';
import '../../models/user_model.dart';
import '../../providers/auth_providers.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final setting = ref.watch(settingControllerProvider).setting;

    return Scaffold(
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Button(
            label: context.tr.updateProfile,
            textColor: Colors.black,
            onPressed: () {
              UpdateProfileScreen().navigate;
            },
          ),
          AppGaps.gap12,
          Button(
            isOutLine: true,
            color: Colors.white,
            label: context.tr.logout,
            onPressed: () {
              ref.watch(bottomNavControllerProvider).changeIndex(0);
              ref.watch(authControllerNotifierProvider).logout();
            },
          ),
        ],
      ).paddingAll(AppSpaces.padding12),
      appBar: BaseAppBar(
        title: context.tr.profile,
        showBack: false,
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppSpaces.screenPadding),
        children: [
          Center(
            child: CircleAvatar(
              backgroundColor: ColorManager.lightPrimaryColor,
              radius: 75.r,
              child: const ClipOval(
                child: BaseCachedImage(
                  // height: 140.h,
                  // width: 140.w,
                  'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',
                  fit: BoxFit
                      .cover, // Ensures the image covers the circular area
                ),
              ),
            ),
          ),

          AppGaps.gap12,

          Text(
            UserModel.currentUser.name,
            style: AppTextStyles.title,
            textAlign: TextAlign.center,
          ),

          AppGaps.gap12,

          // * Address
          BaseTitleSubtitleWidget(
            icon: const Icon(
              Icons.location_on_outlined,
              color: Colors.white,
            ),
            title: '${context.tr.address}:',
            subtitle: UserModel.currentUser.address,
          ),

          // * Phone
          BaseTitleSubtitleWidget(
            icon: const Icon(
              Icons.phone,
              color: Colors.white,
            ),
            title: '${context.tr.phone}:',
            subtitle: UserModel.currentUser.phone,
            textDirection: TextDirection.ltr,
          ),

          // * Safety
          BaseTitleSubtitleWidget(
            onTap: () {
              QuickAlert.show(
                context: context,
                type: QuickAlertType.info,
                confirmBtnText: context.tr.close,
                confirmBtnColor: ColorManager.primaryColor,
                title: context.tr.safety,
                text: setting.privacy,
              );
            },
            icon: const Icon(
              Icons.security,
              color: Colors.white,
            ),
            title: context.tr.safety,
          ),

          // * Support
          BaseTitleSubtitleWidget(
            onTap: () {
              QuickAlert.show(
                context: context,
                type: QuickAlertType.info,
                title: context.tr.support,
                text: setting.supportPhone,
                confirmBtnText: context.tr.call,
                confirmBtnColor: AppColors.successColor,
                showCancelBtn: true,
                cancelBtnText: context.tr.cancel,
              );
            },
            icon: const Icon(
              Icons.support_agent,
              color: Colors.white,
            ),
            title: context.tr.support,
          ),

          //* About App
          BaseTitleSubtitleWidget(
            onTap: () {
              QuickAlert.show(
                context: context,
                type: QuickAlertType.info,
                confirmBtnText: context.tr.close,
                confirmBtnColor: ColorManager.primaryColor,
                title: context.tr.aboutApp,
                text: setting.aboutUs,
              );
            },
            icon: const Icon(
              Icons.info,
              color: Colors.white,
            ),
            title: context.tr.aboutApp,
          ),
        ],
      ),
    );
  }
}
