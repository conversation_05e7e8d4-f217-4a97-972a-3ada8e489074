import 'package:barber_app/src/core/consts/network/api_strings.dart';
import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/fields/text_field.dart';
import 'package:barber_app/src/core/shared/widgets/loading/loading.widget.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/providers/auth_providers.dart';
import 'package:barber_app/src/screens/auth/view/register/register.screen.dart';
import 'package:barber_app/src/screens/main_screen/view/main.screen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/consts/network/api_endpoints.dart';
import '../../../../../core/shared/widgets/verify_bottom_sheet/verify_code_bottom_sheet.dart';
import '../../../../order/providers/order.providers.dart';
import '../../../controllers/verify_otp.controller.dart';

class LoginFieldsWidget extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const LoginFieldsWidget({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final verifyOTPController = ref.watch(verifyOTPControllerProvider);

    final phoneVerified = useState(false);

    void _showResetPasswordDialog(BuildContext context, String phone) {
      final newPasswordController = TextEditingController();
      final confirmPasswordController = TextEditingController();

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('إعادة تعيين كلمة السر'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: newPasswordController,
                decoration: InputDecoration(
                  labelText: context.tr.newPassword,
                  border: const OutlineInputBorder(),
                ),
                obscureText: true,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: confirmPasswordController,
                decoration: InputDecoration(
                  labelText: context.tr.confirmNewPassword,
                  border: const OutlineInputBorder(),
                ),
                obscureText: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (newPasswordController.text !=
                    confirmPasswordController.text) {
                  showToast('كلمات السر غير متطابقة', isError: true);
                  return;
                }

                try {
                  await authController.resetPassword(
                    phone: filteredPhone(phone, withOutCountryCode: true),
                    newPassword: newPasswordController.text,
                  );
                  Navigator.pop(context);
                  Navigator.pop(context);
                  showToast(context.tr.resetPasswordSuccess);
                } catch (e) {
                  showToast(e.toString(), isError: true);
                }
              },
              child: const Text('تأكيد'),
            ),
          ],
        ),
      );
    }

    final phoneController =
        useTextEditingController(text: kDebugMode ? '**********' : '');

    void login() async {
      if (!formKey.currentState!.saveAndValidate()) return;

      final data = formKey.currentState?.instantValue ?? {};

      await authController.login(
        context,
        data: data,
      );

      ref.invalidate(getOrdersFutureProvider);
    }

    void showForgotPasswordSheet() async {
      if (phoneController.text.isEmpty) {
        showToast('يرجى إدخال رقم الهاتف أولاً', isError: true);
        return;
      }

      try {
        await verifyOTPController.signInWithPhoneNumber(
            phone: phoneController.text);

        showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppRadius.radius12),
                    topRight: Radius.circular(AppRadius.radius12))),
            builder: (ctx) => VerifyOtpBottomSheet(
                  onSuccess: (otp) {
                    _showResetPasswordDialog(context, phoneController.text);
                  },
                  phoneNumber: phoneController.text,
                  shouldValidatePhone: true,
                  phoneVerified: phoneVerified,
                ));
      } catch (e) {
        if (e.toString().contains('Daily OTP limit reached')) {
          showToast(context.tr.otpLimitReached, isError: true);
        } else {
          showToast(e.toString(), isError: true);
        }
      }
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppGaps.gap24,

        //! Mobile Number ------------------------------
        BaseTextField(
          name: FieldsConsts.phone,
          controller: phoneController,
          hint: '534576556',
          title: context.tr.mobileNumber,
          textAlign: TextAlign.left,
          withoutEnter: true,
          // suffixIcon: Text(
          //   '+972',
          //   style: AppTextStyles.labelLarge,
          //   textDirection: TextDirection.ltr,
          // ).paddingOnly(top: AppSpaces.padding8 - 2, left: AppSpaces.padding8),
        ),

        AppGaps.gap12,

        //! Password ------------------------------
        BaseTextField(
          name: FieldsConsts.password,
          title: context.tr.password,
          initialValue: kDebugMode ? '123456789' : '',
          hint: '********',
          textInputType: TextInputType.visiblePassword,
          isObscure: true,
          withoutEnter: true,
        ),

        AppGaps.gap8,

        TextButton(
          onPressed: showForgotPasswordSheet,
          child: Text(context.tr.forgotPassword,
              style: AppTextStyles.labelLarge.copyWith(
                color: ColorManager.blue,
              )),
        ),

        AppGaps.gap24,

        Button(
          isLoading: authController.isLoading,
          label: context.tr.login,
          onPressed: login,
          loadingWidget: const LoadingWidget(),
          textColor: Colors.black,
          isWhiteText: false,
        ),

        AppGaps.gap16,

        Text(context.tr.dontHaveAnAccount, style: AppTextStyles.subTitle),

        AppGaps.gap16,

        Button(
          isOutLine: true,
          color: ColorManager.secondaryColor,
          label: context.tr.register,
          onPressed: () {
            const RegisterScreen().navigate;
          },
        ),

        AppGaps.gap8,

        TextButton(
          onPressed: () {
            const MainScreen().navigate;
          },
          child: Text(context.tr.skip,
              style: AppTextStyles.labelLarge.copyWith(
                color: ColorManager.blue,
              )),
        )
      ],
    );
  }
}
