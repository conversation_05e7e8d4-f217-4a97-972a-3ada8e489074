import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../main_screen/view/main.screen.dart';
import '../repositories/verify_otp.repo.dart';

// * Change Notifier ========================================
final verifyOTPControllerProvider = ChangeNotifierProvider<VerifyOTPController>(
  (ref) {
    final verifyOTPRepo = ref.watch(verifyOTPRepoProvider);

    return VerifyOTPController(
      verifyOTPRepo: verifyOTPRepo,
    );
  },
);

class VerifyOTPController extends BaseController {
  final VerifyOTPRepo verifyOTPRepo;

  VerifyOTPController({
    required this.verifyOTPRepo,
  });

  // * Sign in with phone number ================================
  Future signInWithPhoneNumber({
    required String phone,
  }) async {
    try {
      await verifyOTPRepo.login(phone: phone);
    } catch (e) {
      rethrow;
    }

    // await baseControllerFunction(() async {
    //   await verifyOTPRepo.login(phone: phone);
    // });
  }

  // * Verify OTP ================================
  Future<bool> verifyOTP({
    required String otp,
  }) async {
    return await baseControllerFunction(() async {
      final verified = await verifyOTPRepo.verifyOTP(otp);

      return verified;
    });
  }

  // * Navigate to Home ================================
  void _navigateToHome() {
    const MainScreen().navigateReplacement;
  }
}
