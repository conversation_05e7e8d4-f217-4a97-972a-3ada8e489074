import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:math';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:xr_helper/xr_helper.dart';

import '../../../core/consts/app_constants.dart';

final verifyOTPRepoProvider = Provider<VerifyOTPRepo>((ref) {
  return VerifyOTPRepo();
});

class VerifyOTPRepo with BaseRepository {
  String? _generatedOTP;
  DateTime? _otpTimestamp;
  String? _phoneNumber;

  // OTP expires after 5 minutes
  static const int otpExpirationMinutes = 5;

  // Daily OTP limit
  static const num dailyOtpLimit = 3;

  Future<void> login({required String phone}) async {
    try {
      if (!_canSendOTP()) {
        throw Exception('Daily OTP limit reached');
      }

      // Generate 6-digit OTP
      _generatedOTP = _generateOTP();
      _otpTimestamp = DateTime.now();
      _phoneNumber = phone;

      // Send OTP via SMS4Free API
      // if (kReleaseMode) {
      await _sendOTPViaSMS4Free(_phoneNumber ?? '', _generatedOTP!);
      // }

      dev.log(
          'OTP sent successfully to $_phoneNumber\nOTP_Sent: $_generatedOTP');
    } catch (e) {
      dev.log('Error sending OTP: $e');
      rethrow;
    }
  }

  //? Generate 6-digit OTP ------------------------
  String _generateOTP() {
    final random = Random();
    final otp = (100000 + random.nextInt(900000)).toString();
    return otp;
  }

  //? Send OTP via SMS4Free API ------------------------
  Future<void> _sendOTPViaSMS4Free(String phoneNumber, String otp) async {
    final Map<String, dynamic> payload = {
      "key": AppConsts.sms4FreeApiKey,
      "user": AppConsts.sms4FreeUser,
      "pass": AppConsts.sms4FreePass,
      "sender": AppConsts.sms4FreeSender,
      "recipient": phoneNumber,
      "msg": "Your verification code is: $otp"
      // Hebrew: "Your verification code is: $otp"
    };

    try {
      final response = await http.post(
        Uri.parse(AppConsts.sms4FreeApiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(payload),
      );

      dev.log('BODY $payload');

      dev.log('Resss ${response.body}');

      if (response.statusCode == 200) {
        // Increment daily OTP count
        _incrementOtpCount();

        final result = jsonDecode(response.body);

        dev.log(
            "SMS4Free API Response: ${result['status']} - ${result['message']}");

        if (result['message'] != 'Succeeded') {
          throw Exception('SMS sending failed: ${result['message']}');
        }
      } else {
        throw Exception(
            'SMS API failed with status code: ${response.statusCode}');
      }
    } catch (e, s) {
      dev.log('Error sending SMS: $e\n$s');
      throw Exception('Failed to send verification code: $e\n$s');
    }
  }

  Future<bool> verifyOTP(String otp) async {
    return await baseFunction(() async {
      try {
        // Check if OTP exists
        if (_generatedOTP == null || _otpTimestamp == null) {
          Log.e('No OTP generated or OTP session expired');
          return false;
        }

        // Check if OTP has expired (5 minutes)
        final now = DateTime.now();
        final otpAge = now.difference(_otpTimestamp!);
        if (otpAge.inMinutes > otpExpirationMinutes) {
          Log.e('OTP has expired');
          _clearOTPData();
          return false;
        }

        // Verify OTP
        if (otp.trim() == _generatedOTP!.trim()) {
          dev.log('OTP verification successful');
          _clearOTPData();
          return true;
        } else {
          Log.e('Invalid OTP entered');
          return false;
        }
      } catch (e) {
        Log.e('Error during OTP verification: $e');
        return false;
      }
    });
  }

  //? Clear OTP data after verification ------------------------
  void _clearOTPData() {
    _generatedOTP = null;
    _otpTimestamp = null;
    _phoneNumber = null;
  }

  //? Get current user (simplified without Firebase) ------------------------
  Future<String?> getCurrentUser() async {
    // Since we're not using Firebase Auth anymore, we can return the phone number
    // or implement a different user management system
    return _phoneNumber;
  }

  //? Logout (simplified without Firebase) ------------------------
  Future<void> signOut() async {
    try {
      _clearOTPData();
      // Additional logout logic can be added here
      dev.log('User signed out successfully');
    } catch (e) {
      rethrow;
    }
  }

  //? Check if OTP can be sent (daily limit) ------------------------
  bool _canSendOTP() {
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month}-${today.day}';

    final lastDate = GetStorageService.getData(key: LocalKeys.otpLastDate);
    final currentCount =
        GetStorageService.getData(key: LocalKeys.otpDailyCount) ?? 0;

    // If it's a new day, reset the count
    if (lastDate != todayString) {
      GetStorageService.setData(key: LocalKeys.otpLastDate, value: todayString);
      GetStorageService.setData(key: LocalKeys.otpDailyCount, value: 0);
      return true;
    }

    // Check if under daily limit
    return currentCount < dailyOtpLimit;
  }

  //? Increment OTP count ------------------------
  void _incrementOtpCount() {
    final currentCount =
        GetStorageService.getData(key: LocalKeys.otpDailyCount) ?? 0;
    GetStorageService.setData(
        key: LocalKeys.otpDailyCount, value: currentCount + 1);
  }

  //? Get remaining OTP count ------------------------
  num getRemainingOtpCount() {
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month}-${today.day}';

    final lastDate = GetStorageService.getData(key: LocalKeys.otpLastDate);
    final currentCount =
        (GetStorageService.getData(key: LocalKeys.otpDailyCount) as int?) ?? 0;

    // If it's a new day, return full limit
    if (lastDate != todayString) {
      return dailyOtpLimit;
    }

    return dailyOtpLimit - currentCount;
  }
}
