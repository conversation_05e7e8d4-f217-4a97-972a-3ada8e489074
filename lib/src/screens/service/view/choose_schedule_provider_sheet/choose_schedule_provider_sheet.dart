import 'dart:math';

import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/calendar_picker/calendar_picker.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/models/provider_model.dart';
import 'package:barber_app/src/screens/order/providers/order.providers.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/utils/calculate_distance.dart';
import '../../../auth/models/user_model.dart';
import '../../../cart/view/address_screen/choose_address_screen.dart';
import '../../../order/models/order.model.dart';
import '../../../setting/providers/setting_providers.dart';

class ChooseScheduleProviderSheet extends HookConsumerWidget {
  final ProductModel service;
  final ProviderModel? provider;
  final LatLng? selectedLocation;

  const ChooseScheduleProviderSheet({
    super.key,
    required this.service,
    this.provider,
    this.selectedLocation,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDayValue = useState<DateTime>(DateTime.now());
    final selectedTimeSlotValue = useState<AvailableTimeSlotModel?>(null);

    final orderController = ref.watch(orderControllerNotifierProvider);
    final settingsController = ref.watch(settingControllerProvider);
    final freeDistanceRadius = settingsController.setting.freeDistanceKM;
    final afterFreeDistanceCostByKM = settingsController.setting.kmCost;

    // Helper function to get day name from DateTime
    String getDayName(DateTime date) {
      return DateFormat('EEEE', 'en').format(date);
    }

    // Get available time slots for selected day
    List<AvailableTimeSlotModel> getAvailableTimeSlotsForDay(
        DateTime selectedDay) {
      final dayName = getDayName(selectedDay);
      return provider?.availableTimesByDay[dayName] ?? [];
    }

    // Get available time slots that have providers
    List<AvailableTimeSlotModel> getAvailableTimeSlotsWithProviders(
        DateTime selectedDay) {
      return getAvailableTimeSlotsForDay(selectedDay)
          .where((timeSlot) => timeSlot.hasAvailableProviders)
          .toList();
    }

    // If no provider data is provided, show error
    if (provider == null) {
      return Container(
        padding: const EdgeInsets.all(AppSpaces.screenPadding),
        decoration: const BoxDecoration(
          color: ColorManager.backgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppRadius.radius28),
            topRight: Radius.circular(AppRadius.radius28),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              context.tr.noProvidersAvailableInYourArea,
              style: AppTextStyles.title,
            ),
            AppGaps.gap16,
            Button(
              label: context.tr.close,
              onPressed: () => context.back(),
            ),
          ],
        ),
      );
    }

    void onReservePressed() async {
      if (selectedTimeSlotValue.value == null) {
        showToast(context.tr.pleaseSelectTime, isError: true);
        return;
      }

      final selectedTimeSlot = selectedTimeSlotValue.value!;
      final selectedProvider = selectedTimeSlot.firstAvailableProvider;

      if (selectedProvider == null) {
        showToast(context.tr.noProvidersAvailableInYourArea, isError: true);
        return;
      }

      // Create a provider model from the selected provider data
      final providerForOrder = ProviderModel(
        id: selectedProvider.id,
        documentId: selectedProvider.documentId,
        name: selectedProvider.name,
        phone: '',
        address: selectedProvider.address,
        bio: '',
        latLng: selectedLocation, // Use the selected location
      );

      final distance = calculateDistance(selectedLocation, selectedLocation);

      final deliveryCost = distance <= freeDistanceRadius
          ? 0
          : (distance - freeDistanceRadius) * afterFreeDistanceCostByKM;

      final generatedOrderNumber = Random().nextInt(999999);

      final copiedOrder = OrderModel(
        date: selectedProvider.datetime,
        user: UserModel.currentUser,
        address: UserModel.currentUser.address,
        phone: UserModel.currentUser.phone,
        orderNumber: generatedOrderNumber.toString(),
        location: selectedLocation,
        provider: providerForOrder,
        deliveryCost: deliveryCost,
        totalPrice: service.price + deliveryCost,
        productsQuantity: [
          ProductQuantityModel(
            product: service,
            quantity: 1,
            price: service.price,
            totalPrice: service.price,
          )
        ],
        isService: true,
      );

      ChooseAddressScreen(
        order: copiedOrder,
        total: service.price,
        deliveryCost: deliveryCost,
      ).navigate;
    }

    return Container(
      height: 700,
      padding: const EdgeInsets.all(AppSpaces.screenPadding),
      decoration: const BoxDecoration(
        color: ColorManager.backgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppRadius.radius28),
          topRight: Radius.circular(AppRadius.radius28),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          AppGaps.gap8,

          // * Service Name
          Center(
            child: Text(
              '${context.tr.reserve}f ${service.name}',
              style: AppTextStyles.title,
            ),
          ),

          AppGaps.gap16,

          if (selectedTimeSlotValue.value?.firstAvailableProvider != null) ...[
            // * Provider Info
            Container(
              padding: const EdgeInsets.all(AppSpaces.padding12),
              decoration: BoxDecoration(
                color: ColorManager.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppRadius.radius12),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.person,
                    color: ColorManager.primaryColor,
                    size: 20,
                  ),
                  AppGaps.gap8,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          selectedTimeSlotValue
                                  .value?.firstAvailableProvider?.name ??
                              '',
                          style: AppTextStyles.subTitle.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          selectedTimeSlotValue
                                  .value?.firstAvailableProvider?.address ??
                              '',
                          style: AppTextStyles.labelSmall.copyWith(
                            color: ColorManager.lightWhite,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            AppGaps.gap16,
          ],
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${context.tr.sessionTime}:',
                style: AppTextStyles.title,
              ),
              Row(
                children: [
                  const Icon(
                    Icons.access_time,
                    color: ColorManager.lightWhite,
                    size: 16,
                  ),
                  AppGaps.gap4,
                  Text(
                    '${service.sessionTime} ${context.tr.minutes}',
                    style: AppTextStyles.subTitle.copyWith(
                      color: ColorManager.lightWhite,
                    ),
                  ),
                ],
              ),
            ],
          ),

          AppGaps.gap16,

          Text(
            context.tr.day,
            style: AppTextStyles.title,
          ),

          AppGaps.gap12,

          CalendarPickerWidget(selectedDayValue: selectedDayValue),

          AppGaps.gap12,

          Text(
            context.tr.availableTimes,
            style: AppTextStyles.title,
          ),

          AppGaps.gap12,

          Expanded(
            flex: 2,
            child: HookBuilder(
              builder: (context) {
                final availableTimeSlotsForDay =
                    getAvailableTimeSlotsWithProviders(selectedDayValue.value);
                final dayName = getDayName(selectedDayValue.value);

                // Reset selected time when day changes
                useEffect(() {
                  selectedTimeSlotValue.value = null;
                  return null;
                }, [selectedDayValue.value]);

                if (availableTimeSlotsForDay.isEmpty) {
                  return Container(
                    // height: 120.h,
                    alignment: Alignment.center,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.schedule_outlined,
                          size: 48,
                          color: ColorManager.lightWhite.withOpacity(0.5),
                        ),
                        AppGaps.gap8,
                        Text(
                          context.tr.noTimeSlotsAvailable,
                          style: AppTextStyles.subTitle.copyWith(
                            color: ColorManager.lightWhite,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        AppGaps.gap4,
                        Text(
                          '${context.tr.forDay} $dayName',
                          style: AppTextStyles.labelSmall.copyWith(
                            color: ColorManager.lightWhite.withOpacity(0.7),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                return GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    childAspectRatio: 2.5,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: availableTimeSlotsForDay.length,
                  itemBuilder: (context, index) {
                    final timeSlot = availableTimeSlotsForDay[index];
                    final isSelected = selectedTimeSlotValue.value == timeSlot;

                    return GestureDetector(
                      onTap: () {
                        selectedTimeSlotValue.value = timeSlot;
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          border: isSelected
                              ? null
                              : Border.all(
                                  color: ColorManager.primaryColor,
                                  width: 1,
                                ),
                          color: isSelected ? ColorManager.primaryColor : null,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        alignment: Alignment.center,
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            timeSlot.time,
                            style: AppTextStyles.labelSmall.copyWith(
                              color: isSelected
                                  ? Colors.black
                                  : ColorManager.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),

          // Spacer(),

          AppGaps.gap12,

          Button(
            isLoading: orderController.isLoading,
            label: context.tr.reserveNow,
            textColor: Colors.black,
            isPrefixIcon: true,
            onPressed: onReservePressed,
          ),
          AppGaps.gap12,
        ],
      ),
    );
  }
}
