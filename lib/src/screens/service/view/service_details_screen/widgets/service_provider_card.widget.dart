import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/widgets/container/base_container.widget.dart';
import '../../../../auth/models/provider_model.dart';
import '../../provider_details_screen/provider_details.screen.dart';

class ServiceProviderCard extends ConsumerWidget {
  final ProviderModel provider;
  final double? height;

  const ServiceProviderCard({super.key, required this.provider, this.height});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseContainer(
      onTap: () => ProviderDetailsScreen(
        provider: provider,
      ).navigate,
      height: height,
      width: 300.w,
      color: ColorManager.cardColor,
      radius: AppRadius.radius16,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpaces.padding8,
        vertical: AppSpaces.padding8,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BaseCachedImage(
            provider.image?.url ?? '',
            width: 105.w,
            height: 110.h,
            radius: AppRadius.radius16,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    provider.name,
                    style: AppTextStyles.title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // Expanded(
                //   child: Row(
                //     children: [
                //       const Icon(
                //         Icons.star,
                //         color: ColorManager.lightWhite,
                //         size: 16,
                //       ),
                //       AppGaps.gap8,
                //       Text('4.7',
                //           style: AppTextStyles.subTitle
                //               .copyWith(color: ColorManager.lightWhite)),
                //       AppGaps.gap8,
                //       Text('(+100)',
                //           style: AppTextStyles.subTitle
                //               .copyWith(color: ColorManager.lightWhite)),
                //     ],
                //   ),
                // ),
                AppGaps.gap8,
                Expanded(
                  child: Align(
                    alignment: Alignment.bottomRight,
                    child: Text(
                      'السعر: ${provider.services.firstOrNull?.price ?? 0} NIS',
                      style: AppTextStyles.subTitle
                          .copyWith(color: ColorManager.lightWhite),
                    ),
                  ),
                ),
              ],
            ).paddingAll(
              AppSpaces.padding8,
            ),
          ),
        ],
      ),
    );
  }
}
