import 'dart:math';

import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/utils/calculate_distance.dart';
import '../../../../auth/models/provider_model.dart';
import '../../../../auth/models/user_model.dart';
import '../../../../auth/providers/auth_providers.dart';
import '../../../../cart/view/address_screen/choose_address_screen.dart';
import '../../../../order/models/order.model.dart';
import '../../../../order/providers/order.providers.dart';
import '../../../../setting/providers/setting_providers.dart';
import '../../choose_schedule_provider_sheet/choose_schedule_provider_sheet.dart';
import '../../provider_details_screen/choose_map.screen.dart';

class ServiceBottomNavWidget extends HookConsumerWidget {
  final ProductModel service;

  const ServiceBottomNavWidget({super.key, required this.service});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final orderController = ref.watch(orderControllerNotifierProvider);
    final authController = ref.watch(authControllerNotifierProvider);
    final settingsController = ref.watch(settingControllerProvider);
    final selectedLocation = useState<LatLng?>(UserModel.currentUser.latLng);
    final freeDistanceRadius = settingsController.setting.freeDistanceKM;
    final afterFreeDistanceCostByKM = settingsController.setting.kmCost;

    void onSavedClosestProvider(LatLng? userPickedLocation) async {
      final providerData =
          (await authController.getClosestAvailableProviderByLatLng(
        lat: userPickedLocation?.latitude.toString(),
        lng: userPickedLocation?.longitude.toString(),
        serviceDocId: service.documentId,
      ));

      if (providerData == null || providerData.availableTimesByDay.isEmpty) {
        context.back();
        showToast(context.tr.noProvidersAvailableInYourArea, isError: true);
        return;
      }

      // Get current day and time
      final now = DateTime.now();
      final currentDayName = DateFormat('EEEE', 'en').format(now);
      final currentTimeInMinutes = now.hour * 60 + now.minute;

      // Get available time slots for current day
      final todayTimeSlots =
          providerData.availableTimesByDay[currentDayName] ?? [];

      if (todayTimeSlots.isEmpty) {
        context.back();
        showToast(context.tr.noProvidersAvailableToday, isError: true);
        return;
      }

      // Find the closest available time slot after current time
      AvailableTimeSlotModel? closestTimeSlot;
      int minTimeDifference = double.maxFinite.toInt();

      for (final timeSlot in todayTimeSlots) {
        if (timeSlot.hasAvailableProviders) {
          // Parse time (format: "HH:MM")
          final timeParts = timeSlot.time.split(':');
          if (timeParts.length == 2) {
            final slotHour = int.tryParse(timeParts[0]) ?? 0;
            final slotMinute = int.tryParse(timeParts[1]) ?? 0;
            final slotTimeInMinutes = slotHour * 60 + slotMinute;

            // Only consider time slots that are after current time
            if (slotTimeInMinutes >= currentTimeInMinutes) {
              final timeDifference = slotTimeInMinutes - currentTimeInMinutes;
              if (timeDifference < minTimeDifference) {
                minTimeDifference = timeDifference;
                closestTimeSlot = timeSlot;
              }
            }
          }
        }
      }

      if (closestTimeSlot == null) {
        context.back();
        showToast(context.tr.noProvidersAvailableToday, isError: true);
        return;
      }

      // Get the first available provider from the closest time slot
      final selectedProvider = closestTimeSlot.firstAvailableProvider;
      if (selectedProvider == null) {
        context.back();
        showToast(context.tr.noProvidersAvailableInYourArea, isError: true);
        return;
      }

      // Create provider model from selected provider data
      final provider = ProviderModel(
        id: selectedProvider.id,
        documentId: selectedProvider.documentId,
        name: selectedProvider.name,
        phone: '',
        address: selectedProvider.address,
        bio: '',
        latLng: userPickedLocation,
      );

      final distance =
          calculateDistance(userPickedLocation, userPickedLocation);

      final deliveryCost = distance <= freeDistanceRadius
          ? 0
          : (distance - freeDistanceRadius) * afterFreeDistanceCostByKM;

      final generatedOrderNumber = Random().nextInt(999999);

      final copiedOrder = OrderModel(
        date: selectedProvider
            .datetime, // Use the datetime from the selected provider
        user: UserModel.currentUser,
        address: UserModel.currentUser.address,
        phone: UserModel.currentUser.phone,
        orderNumber: generatedOrderNumber.toString(),
        location: userPickedLocation,
        provider: provider,
        deliveryCost: deliveryCost,
        totalPrice: service.price + deliveryCost,
        productsQuantity: [
          ProductQuantityModel(
            product: service,
            quantity: 1,
            price: service.price,
            totalPrice: service.price,
          )
        ],
        isService: true,
      );

      ChooseAddressScreen(
        order: copiedOrder,
        total: service.price,
        deliveryCost: deliveryCost,
      ).navigate;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Button(
          label: context.tr.scheduleReservation,
          isOutLine: true,
          textColor: ColorManager.white,
          color: ColorManager.white,
          onPressed: () {
            ChooseMapScreen(
              selectedLocation: selectedLocation,
              startLocation: UserModel.currentUser.city?.latLng,
              locationRadiusInKM: UserModel.currentUser.city?.radius,
              onSaved: (userPickedLocation) async {
                final provider =
                    await authController.getClosestAvailableProviderByLatLng(
                  lat: userPickedLocation?.latitude.toString(),
                  lng: userPickedLocation?.longitude.toString(),
                  serviceDocId: service.documentId,
                );

                if (provider == null || provider.availableTimesByDay.isEmpty) {
                  context.back();
                  showToast(context.tr.noProvidersAvailableInYourArea,
                      isError: true);
                  return;
                }

                // Show the schedule provider sheet with the provider data
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  builder: (context) {
                    return ChooseScheduleProviderSheet(
                      service: service,
                      provider: provider,
                      selectedLocation: userPickedLocation,
                    );
                  },
                );
              },
            ).navigate;
          },
        ),
        AppGaps.gap16,
        Button(
          isLoading: orderController.isLoading || authController.isLoading,
          label: context.tr.reserveWithClosestPerson,
          textColor: Colors.black,
          onPressed: () async {
            ChooseMapScreen(
              selectedLocation: selectedLocation,
              startLocation: UserModel.currentUser.city?.latLng,
              locationRadiusInKM: UserModel.currentUser.city?.radius,
              onSaved: (userPickedLocation) async {
                onSavedClosestProvider(userPickedLocation);
              },
            ).navigate;
          },
        ),
      ],
    );
  }
}
