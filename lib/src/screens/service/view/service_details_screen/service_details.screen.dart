import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:barber_app/src/screens/service/view/service_details_screen/widgets/service_bottom_nav.widget.dart';
import 'package:barber_app/src/screens/service/view/service_details_screen/widgets/service_provider_card.widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/app_bar/base_appbar.dart';
import '../../../../core/shared/widgets/banner/base_banner.widget.dart';
import '../../../../core/shared/widgets/lists/base_list.dart';
import '../../../auth/providers/auth_providers.dart';

class ServiceDetailsScreen extends ConsumerWidget {
  final ProductModel product;

  const ServiceDetailsScreen({
    super.key,
    required this.product,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getProvidersFuture = ref
        .watch(getProvidersByCityAndServiceFutureProvider(product.documentId));

    return Scaffold(
      appBar: const BaseAppBar(),
      bottomNavigationBar: ServiceBottomNavWidget(
        service: product,
      ).paddingAll(AppSpaces.padding16),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpaces.screenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // * Product Banner Widget
            BaseBannerWidget(
              images: product.imageUrls,
            ),

            AppGaps.gap24,

            // * Product Name
            Text(
              product.name,
              style: AppTextStyles.title,
            ),

            AppGaps.gap24,

            Text(
              '${context.tr.description}:',
              style: AppTextStyles.title,
            ),

            AppGaps.gap24,

            Text(
              product.description,
              style: AppTextStyles.subTitle,
            ),

            // AppGaps.gap24,
            //
            // Text(
            //   '${context.tr.rate}:',
            //   style: AppTextStyles.title,
            // ),

            // AppGaps.gap24,
            //
            // Row(
            //   children: [
            //     const Icon(
            //       Icons.star,
            //       color: ColorManager.lightWhite,
            //       size: 16,
            //     ),
            //     AppGaps.gap8,
            //     Text(product.averageRating.toStringAsFixed(1),
            //         style: AppTextStyles.subTitle
            //             .copyWith(color: ColorManager.lightWhite)),
            //     AppGaps.gap8,
            //     Text('(+${product.ratings.length})',
            //         style: AppTextStyles.subTitle
            //             .copyWith(color: ColorManager.lightWhite)),
            //   ],
            // ),

            AppGaps.gap48,

            Text(
              '${context.tr.availableProvider} (${context.tr.forCity} ${UserModel.currentUser.city?.name ?? ''})',
              style: AppTextStyles.title,
            ),

            AppGaps.gap24,

            getProvidersFuture.get(
              data: (data) {
                final filteredProvidersByCityAndService = data
                    .where((provider) => provider.services
                        .any((service) => service.id == product.id))
                    .toList();

                return BaseList.horizontal(
                  height: 110.h,
                  data: filteredProvidersByCityAndService,
                  itemBuilder: (provider, index) {
                    return ServiceProviderCard(provider: provider);
                  },
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
