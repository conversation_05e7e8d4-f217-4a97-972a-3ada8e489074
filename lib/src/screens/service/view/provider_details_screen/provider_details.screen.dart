import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/tab_bar/base_tab_bar.widget.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/models/provider_model.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:barber_app/src/screens/service/view/provider_details_screen/widgets/provider_details_bottom_nav.widget.dart';
import 'package:barber_app/src/screens/service/view/provider_details_screen/widgets/provider_profile_tab.dart';
import 'package:barber_app/src/screens/service/view/provider_details_screen/widgets/provider_reserve_now_tab.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/app_bar/base_appbar.dart';

class ProviderDetailsScreen extends HookConsumerWidget {
  final ProviderModel provider;
  final ProductModel? service;

  const ProviderDetailsScreen({
    super.key,
    required this.provider,
    this.service,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDayValue = useState(DateTime.now());
    final selectedTimeSlotValue = useState<AvailableTimeSlotModel?>(null);
    final selectedTab = useState(0);

    // Use the passed service or fallback to the first service from provider
    final currentService = service ?? provider.services.firstOrNull;

    return Scaffold(
      appBar: const BaseAppBar(),
      bottomNavigationBar: currentService != null
          ? ProviderDetailsBottomNavWidget(
              provider: provider,
              service: currentService,
              selectedDay: selectedDayValue.value,
              selectedTimeSlot: selectedTimeSlotValue.value,
            ).paddingAll(AppSpaces.padding16)
          : null,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpaces.screenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // * Product Banner Widget
            BaseCachedImage(
              provider.image?.url ?? '',
              height: 200.h,
              width: context.width,
            ),

            AppGaps.gap24,

            BaseTabBarWidget(
              tabs: [
                context.tr.reserveNow,
                context.tr.profile,
              ],
              selectedTab: selectedTab,
            ),

            if (selectedTab.value == 0 && currentService != null)
              ProviderReserveNowTab(
                provider: provider,
                service: currentService,
                selectedDayValue: selectedDayValue,
                selectedTimeSlotValue: selectedTimeSlotValue,
              )
            else if (selectedTab.value == 1)
              ProviderProfileTab(
                provider: provider,
              )
            else
              Center(
                child: Text(
                  context.tr.noServiceSelected,
                  style: AppTextStyles.subTitle.copyWith(
                    color: ColorManager.lightWhite,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
