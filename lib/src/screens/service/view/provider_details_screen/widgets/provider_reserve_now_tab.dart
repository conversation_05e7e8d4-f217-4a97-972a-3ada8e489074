import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/calendar_picker/calendar_picker.dart';
import 'package:barber_app/src/core/shared/widgets/loading/loading.widget.dart';
import 'package:barber_app/src/screens/auth/models/provider_model.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:barber_app/src/screens/auth/providers/auth_providers.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/theme/color_manager.dart';

class ProviderReserveNowTab extends HookConsumerWidget {
  final ProviderModel provider;
  final ProductModel service;
  final ValueNotifier<DateTime> selectedDayValue;
  final ValueNotifier<AvailableTimeSlotModel?> selectedTimeSlotValue;

  const ProviderReserveNowTab({
    super.key,
    required this.provider,
    required this.service,
    required this.selectedDayValue,
    required this.selectedTimeSlotValue,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final providerAvailableTimesAsync = useFuture(
      useMemoized(
        () => authController.getProviderAvailableTimesByLatLng(
          lat: UserModel.currentUser.latLng?.latitude.toString(),
          lng: UserModel.currentUser.latLng?.longitude.toString(),
          serviceDocId: service.documentId,
          providerDocId: provider.documentId,
        ),
        [provider.documentId, service.documentId],
      ),
    );

    // Helper function to get day name from DateTime
    String getDayName(DateTime date, [lang = 'en']) {
      return DateFormat('EEEE', lang).format(date);
    }

    // Get available time slots for selected day
    List<AvailableTimeSlotModel> getAvailableTimeSlotsForDay(
        DateTime selectedDay) {
      final dayName = getDayName(selectedDay);
      final providerData = providerAvailableTimesAsync.data;
      return providerData?.availableTimesByDay[dayName] ?? [];
    }

    // Get available time slots that have providers
    List<AvailableTimeSlotModel> getAvailableTimeSlotsWithProviders(
        DateTime selectedDay) {
      final allTimeSlots = getAvailableTimeSlotsForDay(selectedDay)
          .where((timeSlot) => timeSlot.hasAvailableProviders);

      // If selected day is today, filter out past times
      final now = DateTime.now();
      if (selectedDay.isToday()) {
        final currentTimeInMinutes = now.hour * 60 + now.minute;

        return allTimeSlots.where((timeSlot) {
          // Parse time (format: "HH:MM")
          final timeParts = timeSlot.time.split(':');
          if (timeParts.length == 2) {
            final slotHour = int.tryParse(timeParts[0]) ?? 0;
            final slotMinute = int.tryParse(timeParts[1]) ?? 0;
            final slotTimeInMinutes = slotHour * 60 + slotMinute;

            // Only include time slots that are after current time
            return slotTimeInMinutes >= currentTimeInMinutes;
          }
          return true; // Include if time parsing fails
        }).toList();
      }

      return allTimeSlots.toList();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // * Provider Name
        Text(
          provider.name,
          style: AppTextStyles.title,
        ),

        AppGaps.gap24,

        // * Service Name
        Text(
          service.name,
          style: AppTextStyles.title,
        ),

        AppGaps.gap24,

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${context.tr.sessionTime}:',
              style: AppTextStyles.title,
            ),
            Row(
              children: [
                const Icon(
                  Icons.access_time,
                  color: ColorManager.lightWhite,
                  size: 16,
                ),
                AppGaps.gap4,
                Text(
                  '${service.sessionTime} ${context.tr.minutes}',
                  style: AppTextStyles.subTitle.copyWith(
                    color: ColorManager.lightWhite,
                  ),
                ),
              ],
            ),
          ],
        ),

        AppGaps.gap24,

        Container(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          decoration: const BoxDecoration(
            color: ColorManager.black,
            borderRadius: BorderRadius.all(
              Radius.circular(AppRadius.radius20),
            ),
          ),
          child: Column(
            children: [
              Text(
                context.tr.reservationDetails,
                style: AppTextStyles.title,
              ),
              AppGaps.gap24,
              CalendarPickerWidget(selectedDayValue: selectedDayValue),
              AppGaps.gap24,
              HookBuilder(
                builder: (context) {
                  // Show loading state
                  if (providerAvailableTimesAsync.connectionState ==
                      ConnectionState.waiting) {
                    return const Center(
                      child: LoadingWidget(),
                    );
                  }

                  // Show error state
                  if (providerAvailableTimesAsync.hasError) {
                    return Center(
                      child: Text(
                        context.tr.errorLoadingAvailableTimes,
                        style: AppTextStyles.subTitle.copyWith(
                          color: ColorManager.lightWhite,
                        ),
                      ),
                    );
                  }

                  final availableTimeSlotsForDay =
                      getAvailableTimeSlotsWithProviders(
                          selectedDayValue.value);
                  final dayName = getDayName(selectedDayValue.value, 'ar');

                  // Reset selected time when day changes
                  useEffect(() {
                    selectedTimeSlotValue.value = null;
                    return null;
                  }, [selectedDayValue.value]);

                  if (availableTimeSlotsForDay.isEmpty) {
                    return Container(
                      height: 120.h,
                      alignment: Alignment.center,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.schedule_outlined,
                            size: 48,
                            color: ColorManager.lightWhite.withOpacity(0.5),
                          ),
                          AppGaps.gap8,
                          Text(
                            context.tr.noTimeSlotsAvailable,
                            style: AppTextStyles.subTitle.copyWith(
                              color: ColorManager.lightWhite,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          AppGaps.gap4,
                          Text(
                            '${context.tr.forDay} $dayName',
                            style: AppTextStyles.labelSmall.copyWith(
                              color: ColorManager.lightWhite.withOpacity(0.7),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  return SizedBox(
                    height: 120.h,
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        childAspectRatio: 2.5,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: availableTimeSlotsForDay.length,
                      itemBuilder: (context, index) {
                        final timeSlot = availableTimeSlotsForDay[index];
                        final isSelected =
                            selectedTimeSlotValue.value == timeSlot;
                        final providerCount =
                            timeSlot.availableProviders.length;

                        return GestureDetector(
                          onTap: () {
                            selectedTimeSlotValue.value = timeSlot;
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              border: isSelected
                                  ? null
                                  : Border.all(
                                      color: ColorManager.primaryColor,
                                      width: 1,
                                    ),
                              color:
                                  isSelected ? ColorManager.primaryColor : null,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            alignment: Alignment.center,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  timeSlot.time,
                                  style: AppTextStyles.labelSmall.copyWith(
                                    color: isSelected
                                        ? Colors.black
                                        : ColorManager.primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                if (providerCount > 1) ...[
                                  const SizedBox(height: 2),
                                  Text(
                                    '($providerCount)',
                                    style: AppTextStyles.labelSmall.copyWith(
                                      color: isSelected
                                          ? Colors.black54
                                          : ColorManager.primaryColor
                                              .withOpacity(0.7),
                                      fontSize: 10,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ],
          ),
        )
      ],
    );
  }
}
