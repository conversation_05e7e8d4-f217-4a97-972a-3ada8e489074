import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/screens/auth/models/provider_model.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class ProviderProfileTab extends StatelessWidget {
  final ProviderModel provider;

  const ProviderProfileTab({
    super.key,
    required this.provider,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // * Service Name
        Text(
          "${context.tr.bio}:",
          style: AppTextStyles.title,
        ),

        AppGaps.gap12,

        // * Service Name
        Text(
          provider.bio,
          style: AppTextStyles.subTitle,
        ),

        AppGaps.gap24,
        //
        // Row(
        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //   children: [
        //     Text(
        //       '${context.tr.sessionTime}:',
        //       style: AppTextStyles.title,
        //     ),
        //     Row(
        //       children: [
        //         const Icon(
        //           Icons.access_time,
        //           color: ColorManager.lightWhite,
        //           size: 16,
        //         ),
        //         AppGaps.gap4,
        //         Text(
        //           '${provider.service?.sessionTime} ${context.tr.minutes}',
        //           style: AppTextStyles.subTitle.copyWith(
        //             color: ColorManager.lightWhite,
        //           ),
        //         ),
        //       ],
        //     ),
        //   ],
        // ),
      ],
    );
  }
}
