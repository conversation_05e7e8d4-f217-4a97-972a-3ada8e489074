import 'dart:math';

import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/num_extensions.dart';
import 'package:barber_app/src/core/shared/utils/calculate_distance.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/models/provider_model.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:barber_app/src/screens/cart/view/address_screen/choose_address_screen.dart';
import 'package:barber_app/src/screens/order/models/order.model.dart';
import 'package:barber_app/src/screens/order/providers/order.providers.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:barber_app/src/screens/setting/providers/setting_providers.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../choose_map.screen.dart';

class ProviderDetailsBottomNavWidget extends HookConsumerWidget {
  final ProviderModel provider;
  final ProductModel service;
  final DateTime? selectedDay;
  final AvailableTimeSlotModel? selectedTimeSlot;

  const ProviderDetailsBottomNavWidget({
    super.key,
    required this.provider,
    required this.service,
    required this.selectedDay,
    required this.selectedTimeSlot,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final orderController = ref.watch(orderControllerNotifierProvider);
    final settingsController = ref.watch(settingControllerProvider);
    final selectedLocation = useState<LatLng?>(UserModel.currentUser.latLng);

    final freeDistanceRadius = settingsController.setting.freeDistanceKM;
    final afterFreeDistanceCostByKM = settingsController.setting.kmCost;
    final providerLocation = provider.latLng;

    void onSaved(LatLng? userPickedLocation) async {
      if (selectedTimeSlot == null) {
        showToast(context.tr.pleaseSelectTime, isError: true);
        return;
      }

      // Get the first available provider from the selected time slot
      final selectedProvider = selectedTimeSlot!.firstAvailableProvider;
      if (selectedProvider == null) {
        showToast(context.tr.noProvidersAvailableInYourArea, isError: true);
        return;
      }

      final distance = calculateDistance(providerLocation, userPickedLocation);

      final deliveryCost = distance <= freeDistanceRadius
          ? 0
          : (distance - freeDistanceRadius) * afterFreeDistanceCostByKM;

      final generatedOrderNumber = Random().nextInt(999999);

      final copiedOrder = OrderModel(
        date: selectedProvider.datetime, // Use the datetime from the selected provider
        user: UserModel.currentUser,
        address: UserModel.currentUser.address,
        phone: UserModel.currentUser.phone,
        orderNumber: generatedOrderNumber.toString(),
        location: userPickedLocation,
        provider: provider,
        deliveryCost: deliveryCost,
        totalPrice: service.price + deliveryCost,
        productsQuantity: [
          ProductQuantityModel(
            product: service,
            quantity: 1,
            price: service.price,
            totalPrice: service.price,
          )
        ],
        isService: true,
      );

      ChooseAddressScreen(
        order: copiedOrder,
        total: service.price,
        deliveryCost: deliveryCost,
      ).navigate;
    }

    return Container(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        decoration: const BoxDecoration(
          color: ColorManager.black,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppRadius.radius20),
            topRight: Radius.circular(AppRadius.radius20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // * Product Price
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.monetization_on_outlined,
                      color: ColorManager.lightWhite,
                    ),
                    AppGaps.gap4,
                    Text(
                      '${context.tr.cost}:',
                      style: AppTextStyles.subTitle.copyWith(
                        color: ColorManager.lightWhite,
                      ),
                    ),
                  ],
                ),
                Text(
                  service.price.toCurrency,
                  style: AppTextStyles.subTitle,
                ).paddingSymmetric(horizontal: AppSpaces.padding12),
              ],
            ),

            AppGaps.gap12,

            // * Selected Day
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(
                      CupertinoIcons.calendar,
                      color: ColorManager.lightWhite,
                    ),
                    AppGaps.gap4,
                    Text(
                      selectedDay.formatDateToString,
                      style: AppTextStyles.subTitle.copyWith(
                        color: ColorManager.lightWhite,
                      ),
                    ),
                  ],
                ),
                Text(
                  selectedTimeSlot?.time ?? context.tr.noTimeSelected,
                  style: AppTextStyles.subTitle,
                ).paddingSymmetric(horizontal: AppSpaces.padding12),
              ],
            ),

            AppGaps.gap16,

            Button(
              isLoading: orderController.isLoading,
              label: context.tr.reserveNow,
              textColor: Colors.black,
              isPrefixIcon: true,
              onPressed: () async {
                if (selectedDay == null || selectedTimeSlot == null) {
                  showToast(context.tr.pleaseSelectValidTime, isError: true);
                  return;
                }

                ChooseMapScreen(
                  selectedLocation: selectedLocation,
                  onSaved: onSaved,
                  startLocation: providerLocation,
                  locationRadiusInKM:
                      settingsController.setting.locationRadiusInKM,
                ).navigate;
              },
            ),
          ],
        ));
  }
}
