import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:barber_app/src/core/shared/widgets/choose_map_location/choose_map_location.widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:xr_helper/xr_helper.dart';

class ChooseMapScreen extends HookWidget {
  final ValueNotifier<LatLng?> selectedLocation;
  final void Function(LatLng? userPickedLocation)? onSaved;
  final LatLng? startLocation;
  final num? locationRadiusInKM;

  const ChooseMapScreen({
    super.key,
    required this.selectedLocation,
    this.onSaved,
    this.startLocation,
    this.locationRadiusInKM,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BaseAppBar(
        title: context.tr.chooseYourLocation,
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: ClipRRect(
          borderRadius:
              const BorderRadius.all(Radius.circular(AppRadius.radius20)),
          child: ChooseMapLocationWidget(
            selectedLocation: selectedLocation,
            startLocation: startLocation,
            locationRadiusInKM: locationRadiusInKM,
            onSaved: onSaved,
          ),
        ),
      ),
    );
  }
}
