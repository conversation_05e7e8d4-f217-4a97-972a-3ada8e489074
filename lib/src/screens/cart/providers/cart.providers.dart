import 'package:barber_app/src/core/shared/providers/network_api_service_provider.dart';
import 'package:barber_app/src/screens/cart/controllers/cart.controller.dart';
import 'package:barber_app/src/screens/cart/repositories/cart.repository.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// * Cart Repo Provider ========================================
final cartRepoProvider = Provider<CartRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return CartRepository(networkApiService: networkApiService);
});

// * Cart Change Notifier Provider ========================================
final cartControllerNotifierProvider = ChangeNotifierProvider<CartController>(
  (ref) {
    final cartRepo = ref.watch(cartRepoProvider);

    return CartController(
      cartRepo: cartRepo,
    );
  },
);

// * Cart Provider ========================================
final cartControllerProvider = Provider<CartController>(
  (ref) {
    final cartRepo = ref.watch(cartRepoProvider);

    return CartController(
      cartRepo: cartRepo,
    );
  },
);

// * Get Cart Future Provider ========================================
final getCartFutureProvider = FutureProvider(
  (ref) {
    final cartController = ref.watch(cartControllerProvider);

    return cartController.getCart();
  },
);
