import 'package:barber_app/src/screens/cart/models/cart.model.dart';
import 'package:barber_app/src/screens/cart/repositories/cart.repository.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../models/promo_code.model.dart';

class CartController extends BaseController {
  final CartRepository cartRepo;

  CartController({
    required this.cartRepo,
  });

  // * Get Cart
  Future<List<CartModel>> getCart() async {
    return await baseControllerFunction(
      () async {
        return await cartRepo.getCart();
      },
    );
  }

  // * Update Cart
  Future<void> updateCart({
    required ProductModel product,
    required int quantity,
  }) async {
    return await baseControllerFunction(
      () async {
        return await cartRepo.updateCart(
          product: product,
          quantity: quantity,
        );
      },
    );
  }

  // * Clear Cart
  Future<void> clearCart() async {
    return await baseControllerFunction(
      () async {
        return await cartRepo.clearCart();
      },
    );
  }

  // * Remove From Cart
  Future<void> removeFromCart({
    required ProductModel product,
  }) async {
    return await baseControllerFunction(
      () async {
        return await cartRepo.removeFromCart(
          product: product,
        );
      },
    );
  }

  // * Validate Promo Code
  Future<PromoCodeModel> validatePromoCode({
    required String promoCode,
  }) async {
    return await baseControllerFunction(
      () async {
        return await cartRepo.validatePromoCode(
          promoCode: promoCode,
        );
      },
    );
  }
}
