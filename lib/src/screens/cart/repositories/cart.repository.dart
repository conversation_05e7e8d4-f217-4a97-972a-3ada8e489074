import 'package:barber_app/src/screens/cart/models/cart.model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/consts/network/api_endpoints.dart';
import '../../product/models/product.model.dart';
import '../models/promo_code.model.dart';

class CartRepository with BaseRepository {
  final BaseApiServices networkApiService;

  CartRepository({
    required this.networkApiService,
  });

  // * Get Cart
  Future<List<CartModel>> getCart() async {
    return baseFunction(
      () async {
        final carts = GetStorageService.getData(key: LocalKeys.cart) ?? [];

        final cartsList =
            List<CartModel>.from(carts.map((data) => CartModel.fromJson(data)));

        return cartsList;
      },
    );
  }

  // * Update Cart
  Future<void> updateCart({
    required ProductModel product,
    required int quantity,
  }) async {
    return baseFunction(
      () async {
        final carts = GetStorageService.getData(key: LocalKeys.cart) ?? [];

        final cartsList =
            List<CartModel>.from(carts.map((data) => CartModel.fromJson(data)));

        final existingCartIndex =
            cartsList.indexWhere((cart) => cart.product?.id == product.id);

        if (existingCartIndex != -1) {
          //? Update the quantity of the existing product
          cartsList[existingCartIndex] = CartModel(
            id: cartsList[existingCartIndex].id,
            product: product,
            quantity: quantity,
            totalPrice: product.actualPrice * quantity,
          );
        } else {
          //? Add the new product to the cart
          cartsList.add(CartModel(
            id: cartsList.length + 1,
            product: product,
            quantity: quantity,
            totalPrice: product.actualPrice * quantity,
          ));
        }

        //? Save the updated cart data
        GetStorageService.setData(
          key: LocalKeys.cart,
          value: cartsList.map((e) => e.toJson()).toList(),
        );
      },
    );
  }

  // * Clear Cart
  Future<void> clearCart() async {
    return baseFunction(
      () async {
        GetStorageService.removeKey(key: LocalKeys.cart);
      },
    );
  }

  // * Remove From Cart
  Future<void> removeFromCart({
    required ProductModel product,
  }) async {
    return baseFunction(
      () async {
        final carts = GetStorageService.getData(key: LocalKeys.cart) ?? [];

        final cartsList =
            List<CartModel>.from(carts.map((data) => CartModel.fromJson(data)));

        final existingCartIndex =
            cartsList.indexWhere((cart) => cart.product?.id == product.id);

        if (existingCartIndex != -1) {
          cartsList.removeAt(existingCartIndex);

          //? Save the updated cart data
          GetStorageService.setData(
            key: LocalKeys.cart,
            value: cartsList.map((e) => e.toJson()).toList(),
          );
        }
      },
    );
  }

  // * Validate Promo Code
  Future<PromoCodeModel> validatePromoCode({
    required String promoCode,
  }) async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.validatePromoCode(promoCode);

        final response = await networkApiService.getResponse(url);

        final promoCodeData = response['data'] as List;

        if (promoCodeData.isNotEmpty) {
          final promoCodeModel = PromoCodeModel.fromJson(promoCodeData.first);

          return promoCodeModel;
        }

        return PromoCodeModel.empty();
      },
    );
  }
}
