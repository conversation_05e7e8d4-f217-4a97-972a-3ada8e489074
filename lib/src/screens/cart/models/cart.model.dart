import 'package:barber_app/src/core/shared/models/base_media.model.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';

class CartModel {
  final int? id;
  final int quantity;
  final num totalPrice;
  final ProductModel? product;

  const CartModel({
    this.id,
    this.quantity = 1,
    this.totalPrice = 0,
    this.product,
  });

  factory CartModel.fromJson(Map<String, dynamic> json) {
    return CartModel(
      id: json['id'],
      quantity: json['quantity'] ?? 1,
      totalPrice: json['total_price'] ?? 0,
      product: ProductModel.fromJson(json['product']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quantity': quantity,
      'total_price': totalPrice,
      'product': product?.toJson(),
    };
  }

  factory CartModel.empty() {
    return CartModel(
        id: 0, product: ProductModel.empty(), quantity: 1, totalPrice: 0);
  }

  static List<CartModel> demoCartList = List.generate(
      1,
      (index) => CartModel(
          id: index,
          product:
              const ProductModel(name: "شامبو صن سيلك", price: 220, images: [
            BaseMediaModel(
                url:
                    'http://10.0.2.2:1337/uploads/Shampoo_for_Hair_0fc688a85e.jpg')
          ]),
          quantity: 1,
          totalPrice: 0));
}
