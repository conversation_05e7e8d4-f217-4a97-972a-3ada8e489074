class PromoCodeModel {
  final int id;
  final String documentId;
  final String code;
  final num discount;
  final bool isPercent;
  final bool active;
  final DateTime? endDate;

  const PromoCodeModel({
    this.id = 0,
    this.documentId = '',
    this.code = '',
    this.discount = 0,
    this.isPercent = false,
    this.active = false,
    this.endDate,
  });

  static num calculateDiscount({
    required num total,
    required PromoCodeModel promoCode,
  }) {
    final discount = promoCode.isPercent
        ? total * promoCode.discount / 100
        : promoCode.discount;

    return discount;
  }

  static num calculateTotalAfterDiscount({
    required num total,
    required PromoCodeModel promoCode,
  }) {
    final discount = calculateDiscount(total: total, promoCode: promoCode);

    return total - discount;
  }

  factory PromoCodeModel.fromJson(Map<String, dynamic> json) {
    return PromoCodeModel(
      id: json['id'] ?? 0,
      documentId: json['documentId'] ?? '',
      code: json['code'] ?? '',
      discount: json['discount'] ?? 0,
      isPercent: json['is_percent'] ?? false,
      active: json['active'] ?? false,
      endDate:
          json['end_date'] == null ? null : DateTime.parse(json['end_date']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'documentId': documentId,
      'code': code,
      'discount': discount,
      'is_percent': isPercent,
      'active': active,
      'end_date': endDate?.toIso8601String(),
    };
  }

  factory PromoCodeModel.empty() {
    return const PromoCodeModel(
      id: 0,
      documentId: '',
      code: '',
      discount: 0,
      isPercent: false,
      active: false,
    );
  }
}
