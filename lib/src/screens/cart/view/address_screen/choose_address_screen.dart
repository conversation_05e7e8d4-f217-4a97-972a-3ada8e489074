import 'dart:math';

import 'package:barber_app/src/core/consts/network/api_endpoints.dart';
import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/string_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:barber_app/src/core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:barber_app/src/screens/cart/providers/cart.providers.dart';
import 'package:barber_app/src/screens/order/models/order.model.dart';
import 'package:barber_app/src/screens/payment/view/payment_webview_screen.dart';
import 'package:barber_app/src/screens/setting/providers/setting_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/consts/network/api_strings.dart';
import '../../../../core/shared/utils/on_user_logged.dart';
import '../../../../core/shared/widgets/fields/text_field.dart';
import '../../../main_screen/view/main.screen.dart';
import '../../../order/providers/order.providers.dart';

//? When pay deposit only show it in order field
//? Strapi make sure from time zone

class ChooseAddressScreen extends HookConsumerWidget {
  final OrderModel order;
  final num total;
  final num deliveryCost;

  const ChooseAddressScreen({
    super.key,
    required this.order,
    required this.total,
    required this.deliveryCost,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    final cartController = ref.watch(cartControllerProvider);
    final settingController = ref.watch(settingControllerProvider);
    final orderController = ref.watch(orderControllerNotifierProvider);
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);

    final discount = order.discount;
    final finalTotal = order.totalPrice;

    final providerPercentage = order.provider?.providerPercent ??
        settingController.setting.providerPercent;

    final providerCost = total * providerPercentage / 100;

    // Regular order placement (without payment gateway)
    // void onPlaceOrder() {
    //   onUserLogged(context, onLogged: () async {
    //     if (!formKey.currentState!.saveAndValidate()) return;
    //
    //     final data = formKey.currentState?.instantValue ?? {};
    //
    //     final generatedOrderNumber = Random().nextInt(999999);
    //
    //     final copiedOrder = order.copyWith(
    //       address: data[FieldsConsts.address],
    //       phone: data[FieldsConsts.phone],
    //       note: data[FieldsConsts.note],
    //       user: UserModel.currentUser,
    //       date: order.date ?? DateTime.now(),
    //       orderNumber: generatedOrderNumber.toString(),
    //     );
    //
    //     await orderController.makeOrder(copiedOrder);
    //
    //     bottomNavCtrl.changeIndex(0);
    //
    //     await cartController.clearCart();
    //
    //     ref.invalidate(getCartFutureProvider);
    //     ref.invalidate(getOrdersFutureProvider);
    //
    //     showToast(context.tr.orderMadeSuccessfully);
    //
    //     const MainScreen().navigateReplacement;
    //   });
    // }

    // Payment gateway integration
    void onPayWithGateway({bool isMinimumPayment = false}) {
      onUserLogged(context, onLogged: () async {
        if (!formKey.currentState!.saveAndValidate()) return;

        final data = formKey.currentState?.instantValue ?? {};

        final generatedOrderNumber = Random().nextInt(999999);

        final copiedOrder = order.copyWith(
          address: data[FieldsConsts.address],
          phone: data[FieldsConsts.phone],
          note: data[FieldsConsts.note],
          user: UserModel.currentUser,
          date: order.date ?? DateTime.now(),
          orderNumber: generatedOrderNumber.toString(),
          paidAmount: isMinimumPayment ? providerCost : finalTotal,
        );

        try {
          // Create payment link
          final paymentType = isMinimumPayment ? 'job' : 'order';

          final paymentResponse = await orderController.createPaymentForOrder(
            copiedOrder,
            type: paymentType,
            providerCost: providerCost,
          );

          if (paymentResponse.success && paymentResponse.paymentUrl != null) {
            // Navigate to payment WebView
            final copiedPaymentOrder = copiedOrder.copyWith(
              pageRequestUid: paymentResponse.paymentUrl,
            );

            final paymentResult = await Navigator.of(context).push<bool>(
              MaterialPageRoute(
                builder: (context) => PaymentWebViewScreen(
                  paymentUrl: paymentResponse.paymentUrl!,
                  transactionId: copiedOrder.orderNumber,
                  onPaymentSuccess: () async {
                    // Payment successful, create the order
                    await orderController.makeOrder(
                      copiedPaymentOrder,
                    );
                    await cartController.clearCart();

                    ref.invalidate(getCartFutureProvider);
                    ref.invalidate(getOrdersFutureProvider);
                  },
                  onPaymentFailure: () {
                    // Payment failed, show error message
                    showToast(
                      context.tr.paymentFailed,
                      isError: true,
                    );
                  },
                ),
              ),
            );

            // Handle payment result
            if (paymentResult == true) {
              // Payment was successful
              bottomNavCtrl.changeIndex(0);
              showToast(context.tr.orderMadeSuccessfully);
              const MainScreen().navigateReplacement;
            }
            // If paymentResult is false or null, user cancelled or payment failed
            // Stay on current screen
          } else {
            // Failed to create payment link
            showToast(paymentResponse.errorMessage ?? context.tr.paymentFailed,
                isError: true);
          }
        } catch (e) {
          Log.e('Error processing payment: ${e.toString()}');
        }
      });
    }

    return Scaffold(
      appBar: BaseAppBar(
        title: order.isService
            ? context.tr.confirmServiceAddress
            : context.tr.confirmShippingAddress,
      ),
      body: Column(
        children: [
          Expanded(
            child: FormBuilder(
              key: formKey,
              child: ListView(
                padding: const EdgeInsets.all(AppSpaces.screenPadding),
                children: [
                  // * Address Field
                  BaseTextField(
                    initialValue: UserModel.currentUser.address,
                    name: FieldsConsts.address,
                    title: context.tr.address,
                  ),

                  AppGaps.gap24,

                  // * Phone Field
                  BaseTextField(
                    initialValue: filteredPhone(
                      UserModel.currentUser.phone,
                      withOutCountryCode: true,
                    ),
                    name: FieldsConsts.phone,
                    title: context.tr.phone,
                  ),

                  AppGaps.gap24,

                  // * Note Field
                  BaseTextField(
                    isRequired: false,
                    name: FieldsConsts.note,
                    title: context.tr.note,
                    maxLines: 4,
                  ),

                  AppGaps.gap48,

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        order.isService
                            ? context.tr.totalServiceCost
                            : context.tr.totalPurchases,
                        style: AppTextStyles.subTitle,
                      ),
                      Text(
                        total.toStringAsFixed(2).toCurrency,
                        style: AppTextStyles.subTitle,
                      ),
                    ],
                  ),
                  AppGaps.gap24,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        order.isService
                            ? context.tr.deliveryForYouCost
                            : context.tr.deliveryCost,
                        style: AppTextStyles.subTitle,
                      ),
                      Text(
                        deliveryCost.toStringAsFixed(2).toCurrency,
                        style: AppTextStyles.subTitle,
                      ),
                    ],
                  ),

                  // * If Promo Code is added, show the discount
                  if (discount > 0) ...[
                    AppGaps.gap24,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.tr.discount,
                          style: AppTextStyles.subTitle,
                        ),
                        Text(
                          '$discount'.toCurrency,
                          style: AppTextStyles.subTitle,
                        ),
                      ],
                    ),
                  ],

                  AppGaps.gap24,

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.tr.total,
                        style: AppTextStyles.subTitle,
                      ),
                      Text(
                        finalTotal.toStringAsFixed(2).toCurrency,
                        style: AppTextStyles.subTitle,
                      ),
                    ],
                  ),

                  if (order.isService) ...[
                    AppGaps.gap12,
                    const Divider(
                      color: AppColors.grey,
                      thickness: .5,
                    ),
                    AppGaps.gap12,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.tr.minCostToPay,
                          style: AppTextStyles.subTitle,
                        ),
                        Text(
                          providerCost.toStringAsFixed(2).toCurrency,
                          style: AppTextStyles.subTitle,
                        ),
                      ],
                    ),
                  ],

                  AppGaps.gap24,
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              right: AppSpaces.padding48 - 4,
              left: AppSpaces.screenPadding,
              bottom: AppSpaces.screenPadding,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (order.isService && !orderController.isLoading) ...[
                  Button(
                    label:
                        '${context.tr.pay} ${providerCost.toStringAsFixed(2).toCurrency}',
                    isOutLine: true,
                    textColor: ColorManager.white,
                    color: ColorManager.white,
                    isLoading: orderController.isLoading,
                    onPressed: () => onPayWithGateway(
                      isMinimumPayment: true,
                    ),
                  ),
                  AppGaps.gap16,
                ],
                Button(
                    label: order.isService
                        ? context.tr.payNow
                        : context.tr.orderNow,
                    textColor: Colors.black,
                    isLoading: orderController.isLoading,
                    onPressed: () => onPayWithGateway(isMinimumPayment: false)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
