import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/num_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/cart_widgets/increase_decrease.widget.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/cart/models/cart.model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/widgets/container/base_container.widget.dart';
import '../../../providers/cart.providers.dart';

class CartCardWidget extends HookConsumerWidget {
  final CartModel cart;

  const CartCardWidget({super.key, required this.cart});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartController = ref.read(cartControllerNotifierProvider);
    final product = cart.product;
    final quantity = useState(cart.quantity);

    return BaseContainer(
      height: 110.h,
      width: 300.w,
      color: ColorManager.cardColor,
      radius: AppRadius.radius16,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpaces.padding8,
        vertical: AppSpaces.padding8,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Text(
                    product?.name ?? '',
                    style: AppTextStyles.title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                AppGaps.gap8,
                Flexible(
                  child: Align(
                    alignment: Alignment.bottomRight,
                    child: Text(
                      '${context.tr.price}: ${(product?.actualPrice ?? 0).toCurrency}',
                      style: AppTextStyles.subTitle
                          .copyWith(color: ColorManager.lightWhite),
                    ),
                  ),
                ),
                AppGaps.gap8,
                Flexible(
                    child: IncreaseDecreaseWidget(
                  quantity: quantity,
                  haveTopPadding: true,
                  onChange: (value) {
                    cartController.updateCart(
                      product: product!,
                      quantity: value,
                    );

                    ref.invalidate(getCartFutureProvider);
                  },
                ))
              ],
            ).paddingAll(
              AppSpaces.padding8,
            ),
          ),
          Stack(
            alignment: Alignment.topLeft,
            children: [
              BaseCachedImage(
                product?.thumbnail.url ?? '',
                width: 105.w,
                height: 110.h,
                radius: AppRadius.radius16,
              ),
              InkWell(
                onTap: () {
                  cartController.removeFromCart(product: product!);
                  ref.invalidate(getCartFutureProvider);
                },
                child: const Icon(
                  CupertinoIcons.delete,
                  color: ColorManager.errorColor,
                  size: 18,
                ).paddingAll(AppSpaces.padding8),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
