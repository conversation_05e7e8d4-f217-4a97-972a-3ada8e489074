import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/string_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:barber_app/src/core/shared/widgets/fields/text_field.dart';
import 'package:barber_app/src/core/shared/widgets/lists/base_list.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:barber_app/src/screens/cart/view/cart_screen/widgets/cart_card.widget.dart';
import 'package:barber_app/src/screens/order/models/order.model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import '../../models/promo_code.model.dart';
import '../../providers/cart.providers.dart';
import '../address_screen/choose_address_screen.dart';

class CartScreen extends HookConsumerWidget {
  const CartScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartController = ref.read(cartControllerNotifierProvider);
    final getCartFuture = ref.watch(getCartFutureProvider);
    final promoCodeController = useTextEditingController();
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);

    final promoCode = useState<PromoCodeModel>(PromoCodeModel.empty());

    final isPromoCodeApplied = promoCode.value != PromoCodeModel.empty();

    void verifyCoupon() async {
      if (promoCode.value != PromoCodeModel.empty()) {
        promoCode.value = PromoCodeModel.empty();
        return;
      }

      promoCode.value = await cartController.validatePromoCode(
          promoCode: promoCodeController.text.toLowerCase());

      if (promoCode.value != PromoCodeModel.empty()) {
        showToast(context.tr.addedSuccessfully);
      } else {
        showToast(context.tr.invalidPromo, isError: true);
      }
    }

    return getCartFuture.get(
      data: (cartList) {
        final cartTotal = cartList.fold(0.0,
            (previousValue, element) => previousValue + element.totalPrice);

        final deliveryCost = UserModel.userCity.deliveryCost;
        final finalTotal = PromoCodeModel.calculateTotalAfterDiscount(
                total: cartTotal, promoCode: promoCode.value) +
            deliveryCost;

        if (cartList.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  CupertinoIcons.cart,
                  size: 100,
                  color: ColorManager.lightGrey,
                ),
                AppGaps.gap16,
                Text(
                  context.tr.cartIsEmpty,
                  style: AppTextStyles.whiteSubHeadLine,
                ),
              ],
            ),
          );
        }

        return Scaffold(
            bottomNavigationBar: Row(
              children: [
                Expanded(
                  child: Button(
                    label: context.tr.confirmOrder,
                    textColor: Colors.black,
                    onPressed: () {
                      final productsQuantity = cartList
                          .map((e) => ProductQuantityModel(
                                product: e.product,
                                quantity: e.quantity,
                                price: e.product?.actualPrice ?? 0,
                                totalPrice: e.totalPrice,
                              ))
                          .toList();

                      final order = OrderModel(
                        promoCode: promoCode.value,
                        totalPrice: finalTotal,
                        deliveryCost: deliveryCost,
                        date: DateTime.now(),
                        productsQuantity: productsQuantity,
                        discount: isPromoCodeApplied
                            ? PromoCodeModel.calculateDiscount(
                                total: cartTotal, promoCode: promoCode.value)
                            : 0,
                        user: UserModel.currentUser,
                      );

                      ChooseAddressScreen(
                        order: order,
                        total: cartTotal,
                        deliveryCost: deliveryCost,
                      ).navigate;
                    },
                  ),
                ),
                AppGaps.gap12,
                Expanded(
                  child: Button(
                    isOutLine: true,
                    color: Colors.white,
                    label: context.tr.continueShopping,
                    onPressed: () {
                      bottomNavCtrl.changeIndex(0);
                    },
                  ),
                ),
              ],
            ).paddingAll(AppSpaces.padding12),
            appBar: BaseAppBar(
              title: context.tr.cart,
              showBack: false,
            ),
            body: Padding(
              padding: const EdgeInsets.all(AppSpaces.screenPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: BaseList(
                      data: cartList,
                      itemBuilder: (cart, index) {
                        return CartCardWidget(
                          cart: cart,
                        );
                      },
                    ),
                  ),
                  AppGaps.gap24,
                  BaseTextField(
                    name: 'promo',
                    readOnly: isPromoCodeApplied,
                    controller: promoCodeController,
                    title: context.tr.promo,
                    hint: context.tr.promo,
                    suffixIcon: InkWell(
                      onTap: verifyCoupon,
                      child: Text(
                        isPromoCodeApplied
                            ? context.tr.remove
                            : context.tr.apply,
                        style: AppTextStyles.labelLarge
                            .copyWith(color: ColorManager.primaryColor),
                      ).paddingOnly(
                          top: AppSpaces.padding4, left: AppSpaces.padding4),
                    ),
                  ),
                  AppGaps.gap24,
                  Text(
                    context.tr.totalCalculation,
                    style: AppTextStyles.title,
                  ),
                  AppGaps.gap24,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.tr.totalPurchases,
                        style: AppTextStyles.subTitle,
                      ),
                      Text(
                        '$cartTotal'.toCurrency,
                        style: AppTextStyles.subTitle,
                      ),
                    ],
                  ),
                  AppGaps.gap24,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.tr.deliveryCost,
                        style: AppTextStyles.subTitle,
                      ),
                      Text(
                        '$deliveryCost'.toCurrency,
                        style: AppTextStyles.subTitle,
                      ),
                    ],
                  ),
                  // * If Promo Code is added, show the discount
                  if (isPromoCodeApplied) ...[
                    AppGaps.gap24,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.tr.discount,
                          style: AppTextStyles.subTitle,
                        ),
                        Text(
                          '${PromoCodeModel.calculateDiscount(total: cartTotal, promoCode: promoCode.value)}'
                              .toCurrency,
                          style: AppTextStyles.subTitle,
                        ),
                      ],
                    ),
                  ],
                  AppGaps.gap24,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.tr.total,
                        style: AppTextStyles.subTitle,
                      ),
                      Text(
                        '$finalTotal'.toCurrency,
                        style: AppTextStyles.subTitle,
                      ),
                    ],
                  ),
                ],
              ),
            ));
      },
    );
  }
}
