import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:barber_app/src/screens/category/view/widgets/sub_categories_list.widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../home/<USER>/widgets/products_list.widget.dart';
import '../models/category.model.dart';

class CategoriesScreen extends HookWidget {
  final CategoryModel category;

  const CategoriesScreen({
    super.key,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    final selectedSubCat = useState(category.subCategories.firstOrNull);

    return Scaffold(
      appBar: BaseAppBar(
        title: category.name,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpaces.screenPadding),
        child: Column(
          children: [
            GestureDetector(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) => Dialog(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius16),
                    ),
                    child: ClipRRect(
                        borderRadius: BorderRadius.circular(AppRadius.radius16),
                        child: Image.network(
                          category.image?.url ?? '',
                        )),
                  ),
                );
              },
              child: BaseCachedImage(
                category.image?.url ?? '',
                height: 160.h,
                width: double.infinity,
                fit: BoxFit.fill,
                radius: AppRadius.radius16,
                showErrorWidget: category.image?.url.isNotEmpty ?? false,
              ),
            ),
            AppGaps.gap16,
            SubCategoriesListWidget(
              subCategories: category.subCategories,
              selectedSubCat: selectedSubCat,
            ),
            AppGaps.gap16,
            ProductsListWidget(
              title: context.tr.latestProducts,
              products: selectedSubCat.value?.products ?? [],
            ),
          ],
        ),
      ),
    );
  }
}
