import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/container/base_container.widget.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/category/models/sub_category.model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/lists/base_list.dart';

class SubCategoriesListWidget extends ConsumerWidget {
  final List<SubCategoryModel> subCategories;
  final ValueNotifier<SubCategoryModel?> selectedSubCat;

  const SubCategoriesListWidget(
      {super.key, required this.subCategories, required this.selectedSubCat});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(context.tr.products, style: AppTextStyles.title),
        AppGaps.gap8,
        _buildCategoriesList(),
      ],
    );
  }

  Widget _buildCategoriesList() {
    return BaseList.horizontal(
      height: 45.h,
      data: subCategories,
      padding: EdgeInsets.zero,
      itemBuilder: (category, index) {
        final isSelected = selectedSubCat.value == category;

        return BaseContainer(
          onTap: () => selectedSubCat.value = category,
          width: 150.w,
          color: isSelected ? ColorManager.primaryColor : ColorManager.black,
          borderColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpaces.padding12,
            vertical: AppSpaces.padding8,
          ),
          child: Row(
            children: [
              category.image?.url.networkImage(
                    height: 40,
                    width: 40,
                    radius: AppRadius.radius100,
                    fit: BoxFit.cover,
                  ) ??
                  ''.networkImage(
                    height: 40,
                    width: 40,
                    radius: AppRadius.radius100,
                    fit: BoxFit.cover,
                  ),
              AppGaps.gap12,
              Expanded(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(category.name,
                      style: AppTextStyles.subTitle.copyWith(
                          fontSize: 15,
                          color: isSelected ? Colors.black : Colors.white)),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
