import 'package:barber_app/src/core/shared/models/base_media.model.dart';
import 'package:barber_app/src/screens/category/models/sub_category.model.dart';

class CategoryModel {
  final int id;
  final String documentId;
  final String name;
  final DateTime? createdAt;
  final BaseMediaModel? image;
  final List<SubCategoryModel> subCategories;

  const CategoryModel({
    this.id = 0,
    this.documentId = '',
    this.name = '',
    this.createdAt,
    this.image,
    this.subCategories = const [],
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id'] ?? 0,
      documentId: json['documentId'] ?? '',
      name: json['name'] ?? '',
      createdAt:
          json['createdAt'] == null ? null : DateTime.parse(json['createdAt']),
      image:
          json['image'] != null ? BaseMediaModel.fromJson(json['image']) : null,
      subCategories: (json['sub_categories'] as List<dynamic>?)
              ?.map((category) => SubCategoryModel.fromJson(category))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'documentId': documentId,
      'name': name,
      'image': image?.toJson(),
    };
  }

  factory CategoryModel.empty() {
    return CategoryModel(
      id: 0,
      documentId: '',
      name: '',
      createdAt: DateTime.now(),
      image: null,
    );
  }
}
