import 'package:barber_app/src/core/shared/models/base_media.model.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';

class SubCategoryModel {
  final int id;
  final String documentId;
  final String name;
  final DateTime? createdAt;
  final BaseMediaModel? image;
  final List<ProductModel> products;

  const SubCategoryModel({
    this.id = 0,
    this.documentId = '',
    this.name = '',
    this.createdAt,
    this.image,
    this.products = const [],
  });

  factory SubCategoryModel.fromJson(Map<String, dynamic> json) {
    return SubCategoryModel(
      id: json['id'] ?? 0,
      documentId: json['documentId'] ?? '',
      name: json['name'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
      image:
          json['image'] != null ? BaseMediaModel.fromJson(json['image']) : null,
      products: (json['products'] as List<dynamic>?)
              ?.map((category) => ProductModel.fromJson(category))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'documentId': documentId,
      'name': name,
      'image': image?.toJson(),
    };
  }

  factory SubCategoryModel.empty() {
    return SubCategoryModel(
      id: 0,
      documentId: '',
      name: '',
      createdAt: DateTime.now(),
      image: null,
    );
  }
}
