import 'package:barber_app/src/core/consts/network/api_endpoints.dart';
import 'package:barber_app/src/screens/category/models/category.model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../models/sub_category.model.dart';

class CategoryRepository with BaseRepository {
  final BaseApiServices networkApiService;

  CategoryRepository({
    required this.networkApiService,
  });

  // * Get Categories
  Future<List<CategoryModel>> getCategories() async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.categories;

        final response = await networkApiService.getResponse(url);

        final categories = response['data'] as List;

        final categoriesList =
            categories.map((data) => CategoryModel.fromJson(data)).toList();

        return categoriesList;
      },
    );
  }

  // * Get Sub Categories
  Future<List<SubCategoryModel>> getSubCategories() async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.subCategories;

        final response = await networkApiService.getResponse(url);

        final categories = response['data'] as List;

        final categoriesList =
            categories.map((data) => SubCategoryModel.fromJson(data)).toList();

        return categoriesList;
      },
    );
  }

  // * Add Categories
  Future<void> addCategories({
    required Map<String, dynamic> data,
  }) async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.categories;

        await networkApiService.postResponse(
          url,
          body: data,
        );
      },
    );
  }
}
