import 'package:barber_app/src/core/shared/providers/network_api_service_provider.dart';
import 'package:barber_app/src/screens/category/controllers/category.controller.dart';
import 'package:barber_app/src/screens/category/repositories/category.repository.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// * Categories Repo Provider ========================================
final categoriesRepoProvider = Provider<CategoryRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return CategoryRepository(networkApiService: networkApiService);
});

// * Categories Change Notifier Provider ========================================
final categoriesControllerNotifierProvider =
    ChangeNotifierProvider<CategoryController>(
  (ref) {
    final categoriesRepo = ref.watch(categoriesRepoProvider);

    return CategoryController(
      categoryRepo: categoriesRepo,
    );
  },
);

// * Categories Provider ========================================
final categoriesControllerProvider = Provider<CategoryController>(
  (ref) {
    final categoriesRepo = ref.watch(categoriesRepoProvider);

    return CategoryController(
      categoryRepo: categoriesRepo,
    );
  },
);

// * Get Categories Future Provider ========================================
final getCategoriesFutureProvider = FutureProvider(
  (ref) {
    final categoriesController = ref.watch(categoriesControllerProvider);

    return categoriesController.getCategories();
  },
);

// * Get Sub Categories Future Provider ========================================
final getSubCategoriesFutureProvider = FutureProvider(
  (ref) {
    final categoriesController = ref.watch(categoriesControllerProvider);

    return categoriesController.getSubCategories();
  },
);
