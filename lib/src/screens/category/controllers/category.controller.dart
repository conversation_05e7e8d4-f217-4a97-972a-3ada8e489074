import 'package:barber_app/src/screens/category/models/category.model.dart';
import 'package:barber_app/src/screens/category/models/sub_category.model.dart';
import 'package:barber_app/src/screens/category/repositories/category.repository.dart';
import 'package:xr_helper/xr_helper.dart';

class CategoryController extends BaseController {
  final CategoryRepository categoryRepo;

  CategoryController({
    required this.categoryRepo,
  });

  // * Get Categories
  Future<List<CategoryModel>> getCategories() async {
    return await baseControllerFunction(
      () async {
        return await categoryRepo.getCategories();
      },
    );
  }

  // * Get Sub Categories
  Future<List<SubCategoryModel>> getSubCategories() async {
    return await baseControllerFunction(
      () async {
        return await categoryRepo.getSubCategories();
      },
    );
  }

  // * Add Categories
  Future<void> addCategory({
    required Map<String, dynamic> data,
  }) async {
    return await baseControllerFunction(
      () async {
        return await categoryRepo.addCategories(data: data);
      },
    );
  }
}
