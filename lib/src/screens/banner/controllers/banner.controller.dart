import 'package:barber_app/src/screens/banner/models/banner.model.dart';
import 'package:barber_app/src/screens/banner/repositories/banner.repository.dart';
import 'package:xr_helper/xr_helper.dart';

class BannerController extends BaseController {
  final BannerRepository bannerRepo;

  BannerController({
    required this.bannerRepo,
  });

  // * Get Banner
  Future<List<BannerModel>> getBanners() async {
    return await baseControllerFunction(
      () async {
        return await bannerRepo.getBanners();
      },
    );
  }
}
