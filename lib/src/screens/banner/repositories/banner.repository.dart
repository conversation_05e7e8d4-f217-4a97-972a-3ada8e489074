import 'package:barber_app/src/core/consts/network/api_endpoints.dart';
import 'package:barber_app/src/screens/banner/models/banner.model.dart';
import 'package:xr_helper/xr_helper.dart';

class BannerRepository with BaseRepository {
  final BaseApiServices networkApiService;

  BannerRepository({
    required this.networkApiService,
  });

  // * Get Banner
  Future<List<BannerModel>> getBanners() async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.banners;

        final response = await networkApiService.getResponse(url);

        final banner = response['data'] as List;

        final bannerList =
            banner.map((data) => BannerModel.fromJson(data)).toList();

        return bannerList;
      },
    );
  }
}
