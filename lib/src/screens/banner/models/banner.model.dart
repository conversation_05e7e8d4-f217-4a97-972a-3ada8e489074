import 'package:barber_app/src/core/shared/models/base_media.model.dart';
import 'package:barber_app/src/core/shared/models/base_model.dart';

class BannerModel extends BaseModel {
  final bool isActive;
  final DateTime? startDate;
  final DateTime? endDate;
  final BaseMediaModel? image;
  final String url;

  const BannerModel({
    super.id,
    super.documentId,
    super.createdAt,
    this.image,
    this.startDate,
    this.endDate,
    this.url = '',
    this.isActive = true,
  });

  factory BannerModel.fromJson(Map<String, dynamic> json) {
    return BannerModel(
      id: json['id'],
      documentId: json['documentId'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
      url: json['url'] ?? '',
      startDate:
          json['startDate'] != null ? DateTime.parse(json['startDate']) : null,
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      isActive: json['isActive'] ?? true,
      image:
          json['image'] != null ? BaseMediaModel.fromJson(json['image']) : null,
    );
  }

  @override
  List<Object?> get props => [
        id,
        documentId,
        createdAt,
        startDate,
        endDate,
        isActive,
        image,
        url,
      ];
}
