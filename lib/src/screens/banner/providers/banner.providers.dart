import 'package:barber_app/src/core/shared/providers/network_api_service_provider.dart';
import 'package:barber_app/src/screens/banner/controllers/banner.controller.dart';
import 'package:barber_app/src/screens/banner/repositories/banner.repository.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// * Banner Repo Provider ========================================
final bannerRepoProvider = Provider<BannerRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return BannerRepository(networkApiService: networkApiService);
});

// * Banner Change Notifier Provider ========================================
final bannerControllerNotifierProvider =
    ChangeNotifierProvider<BannerController>(
  (ref) {
    final bannerRepo = ref.watch(bannerRepoProvider);

    return BannerController(
      bannerRepo: bannerRepo,
    );
  },
);

// * Banner Provider ========================================
final bannerControllerProvider = Provider<BannerController>(
  (ref) {
    final bannerRepo = ref.watch(bannerRepoProvider);

    return BannerController(
      bannerRepo: bannerRepo,
    );
  },
);

// * Get Banners Future Provider ========================================
final getBannersFutureProvider = FutureProvider(
  (ref) {
    final bannerController = ref.watch(bannerControllerProvider);

    return bannerController.getBanners();
  },
);
