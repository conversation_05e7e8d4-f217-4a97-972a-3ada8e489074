import 'package:barber_app/generated/assets.gen.dart';
import 'package:barber_app/src/screens/auth/view/login/login.screen.dart';
import 'package:barber_app/src/screens/main_screen/view/main.screen.dart';
import 'package:barber_app/src/screens/setting/providers/setting_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class SplashScreen extends HookConsumerWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingController = ref.read(settingControllerProvider);

    final loggedIn = GetStorageService.hasData(key: LocalKeys.user);
    final opacity = useState(0.0);

    useEffect(() {
      final navigateWidget =
          loggedIn ? const MainScreen() : const LoginScreen();

      Future.delayed(const Duration(milliseconds: 500), () {
        opacity.value = 1.0;
      });

      settingController.getSetting().then(
        (value) {
          Future.delayed(const Duration(seconds: 2), () {
            navigateWidget.navigate;
          });
        },
      );

      return () {};
    }, []);

    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppSpaces.padding64),
          child: AnimatedOpacity(
            opacity: opacity.value,
            duration: const Duration(seconds: 1),
            child: Assets.images.logo.image(fit: BoxFit.cover),
          ),
        ),
      ),
    );
  }
}
