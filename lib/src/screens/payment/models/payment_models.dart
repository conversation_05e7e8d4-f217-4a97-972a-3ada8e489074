class PaymentTransaction {
  final String id;
  final double amount;
  final String type; // 'job' or 'order'
  final PaymentCustomer customer;
  final String? orderId;
  final String? taskId;

  const PaymentTransaction({
    required this.id,
    required this.amount,
    required this.type,
    required this.customer,
    this.orderId,
    this.taskId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'type': type,
      'customer': customer.toJson(),
      'order_id': orderId,
      'task_id': taskId,
    };
  }

  factory PaymentTransaction.fromJson(Map<String, dynamic> json) {
    return PaymentTransaction(
      id: json['id'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      type: json['type'] ?? 'order',
      customer: PaymentCustomer.fromJson(json['customer'] ?? {}),
      orderId: json['order_id'],
      taskId: json['task_id'],
    );
  }
}

class PaymentCustomer {
  final String name;
  final String email;
  final String phone;

  const PaymentCustomer({
    required this.name,
    required this.email,
    required this.phone,
  });

  Map<String, dynamic> toJson() {
    return {
      'customer_name': name,
      'email': email,
      'phone': phone,
    };
  }

  factory PaymentCustomer.fromJson(Map<String, dynamic> json) {
    return PaymentCustomer(
      name: json['customer_name'] ?? json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
    );
  }
}

class PaymentRequest {
  final String paymentPageUid;
  final String refUrlSuccess;
  final String refUrlFailure;
  final String refUrlCallback;
  final PaymentCustomer customer;
  final double amount;
  final String currencyCode;
  final bool sendEmailApproval;
  final bool sendEmailFailure;
  final bool hideIdentificationId;

  const PaymentRequest({
    required this.paymentPageUid,
    required this.refUrlSuccess,
    required this.refUrlFailure,
    required this.refUrlCallback,
    required this.customer,
    required this.amount,
    this.currencyCode = 'ILS',
    this.sendEmailApproval = true,
    this.sendEmailFailure = false,
    this.hideIdentificationId = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'payment_page_uid': paymentPageUid,
      'refURL_success': refUrlSuccess,
      'refURL_failure': refUrlFailure,
      'refURL_callback': refUrlCallback,
      'customer': customer.toJson(),
      'amount': amount,
      'currency_code': currencyCode,
      'sendEmailApproval': sendEmailApproval,
      'sendEmailFailure': sendEmailFailure,
      'hide_identification_id': hideIdentificationId,
    };
  }
}

class PaymentResponse {
  final bool success;
  final String? paymentUrl;
  final String? pageRequestUid;
  final String? errorMessage;
  final Map<String, dynamic>? data;

  const PaymentResponse({
    required this.success,
    this.paymentUrl,
    this.pageRequestUid,
    this.errorMessage,
    this.data,
  });

  // factory PaymentResponse.fromJson(Map<String, dynamic> json) {
  //   final data = json['data'] as Map<String, dynamic>?;
  //   return PaymentResponse(
  //     success: json['success'] ?? false,
  //     paymentUrl: data?['payment_page_link'],
  //     pageRequestUid: data?['page_request_uid'],
  //     errorMessage: json['error_message'] ?? json['message'],
  //     data: data,
  //   );
  // }

  factory PaymentResponse.success(
      String paymentUrl, Map<String, dynamic>? data) {
    return PaymentResponse(
      success: true,
      paymentUrl: paymentUrl,
      data: data,
    );
  }

  factory PaymentResponse.error(String errorMessage) {
    return PaymentResponse(
      success: false,
      errorMessage: errorMessage,
    );
  }
}
