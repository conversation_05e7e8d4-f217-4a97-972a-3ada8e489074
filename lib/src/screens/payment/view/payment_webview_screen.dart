import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../core/shared/widgets/app_bar/base_appbar.dart';
import '../../../core/theme/color_manager.dart';

class PaymentWebViewScreen extends StatefulWidget {
  final String paymentUrl;
  final String transactionId;
  final VoidCallback? onPaymentSuccess;
  final VoidCallback? onPaymentFailure;

  const PaymentWebViewScreen({
    super.key,
    required this.paymentUrl,
    required this.transactionId,
    this.onPaymentSuccess,
    this.onPaymentFailure,
  });

  @override
  State<PaymentWebViewScreen> createState() => _PaymentWebViewScreenState();
}

class _PaymentWebViewScreenState extends State<PaymentWebViewScreen> {
  late WebViewController controller;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading progress if needed
          },
          onPageStarted: (String url) {
            setState(() {
              isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              isLoading = false;
            });

            // Check for payment completion URLs
            if (url.contains('google')) {
              _handlePaymentSuccess();
            } else if (url.contains('facebook')) {
              _handlePaymentFailure();
            }
          },
          onWebResourceError: (WebResourceError error) {
            // Handle web resource errors
            debugPrint('WebView error: ${error.description}');
          },
          onNavigationRequest: (NavigationRequest request) {
            // Allow all navigation requests
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.paymentUrl));
  }

  void _handlePaymentSuccess() {
    // Show success message
    // showToast('Payment completed successfully!');

    // Call success callback
    if (widget.onPaymentSuccess != null) {
      widget.onPaymentSuccess!();
    }

    // Navigate back or to success screen
    Navigator.of(context).pop(true);
  }

  void _handlePaymentFailure() {
    // Show failure message
    // showToast('Payment failed. Please try again.');

    // Call failure callback
    if (widget.onPaymentFailure != null) {
      widget.onPaymentFailure!();
    }

    // Navigate back to payment screen
    Navigator.of(context).pop(false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BaseAppBar(
        title: context.tr.payment,
      ),
      // appBar: AppBar(
      //   title: const Text('Payment'),
      //   backgroundColor: Colors.transparent,
      //   elevation: 0,
      //   surfaceTintColor: Colors.transparent,
      //   centerTitle: true,
      //   leading: IconButton(
      //     icon: const Icon(Icons.close, color: Colors.white),
      //     onPressed: () {
      //       // Show confirmation dialog before closing
      //       _showExitConfirmationDialog();
      //     },
      //   ),
      // ),
      body: Stack(
        children: [
          WebViewWidget(controller: controller),
          if (isLoading)
            const Center(
              child: CircularProgressIndicator(
                color: ColorManager.primaryColor,
              ),
            ),
        ],
      ),
    );
  }

  void _showExitConfirmationDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Cancel Payment'),
          content: const Text(
              'Are you sure you want to cancel the payment process?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
              },
              child: const Text('Continue Payment'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(false); // Close payment screen
              },
              child: const Text('Cancel Payment'),
            ),
          ],
        );
      },
    );
  }
}
