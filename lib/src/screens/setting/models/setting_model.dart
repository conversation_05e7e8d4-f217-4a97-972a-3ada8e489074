import 'package:barber_app/src/core/shared/models/base_model.dart';

class SettingModel extends BaseModel {
  //"id": 5,
  // "documentId": "nu06zfvqj7uup56ta5dlk0of",
  // "support_phone": "05124124214",
  // "about_us": "TEST About US",
  // "privacy": "TEST **Privacy**",
  // "createdAt": "2025-03-11T14:57:38.179Z",
  // "updatedAt": "2025-03-25T02:07:19.593Z",
  // "publishedAt": "2025-03-25T02:07:19.602Z",
  // "free_distance": 40,
  // "km_cost": 12,
  // "provider_percent": 50,
  // "location_radius": 20
  final String supportPhone;
  final String aboutUs;
  final String privacy;
  final num freeDistanceKM;
  final num kmCost;
  final num providerPercent;
  final num locationRadiusInKM;

  const SettingModel({
    super.id,
    super.documentId,
    this.supportPhone = '',
    this.aboutUs = '',
    this.privacy = '',
    this.freeDistanceKM = 0,
    this.kmCost = 0,
    this.providerPercent = 0,
    this.locationRadiusInKM = 0,
  });

  // * From Json
  factory SettingModel.fromJson(Map<String, dynamic> json) {
    return SettingModel(
      id: json['id'],
      documentId: json['documentId'] ?? '',
      supportPhone: json['support_phone'] ?? '',
      aboutUs: json['about_us'] ?? '',
      privacy: json['privacy'] ?? '',
      freeDistanceKM: json['free_distance'] ?? 0,
      kmCost: json['km_cost'] ?? 0,
      providerPercent: json['provider_percent'] ?? 0,
      locationRadiusInKM: json['location_radius'] ?? 0,
    );
  }

  // * To Json
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'documentId': documentId,
      'support_phone': supportPhone,
      'about_us': aboutUs,
      'privacy': privacy,
      'free_distance': freeDistanceKM,
      'km_cost': kmCost,
      'provider_percent': providerPercent,
      'location_radius': locationRadiusInKM,
    };
  }

  factory SettingModel.empty() => const SettingModel();

  @override
  List<Object?> get props => [];
}
