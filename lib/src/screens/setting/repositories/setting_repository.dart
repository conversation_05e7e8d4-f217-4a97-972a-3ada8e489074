import 'package:barber_app/src/core/consts/network/api_endpoints.dart';
import 'package:xr_helper/xr_helper.dart';

import '../models/setting_model.dart';

class SettingRepository with BaseRepository {
  final BaseApiServices networkApiService;

  SettingRepository({
    required this.networkApiService,
  });

  // * Get Setting
  Future<SettingModel> getSetting() async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.setting;

        final response = await networkApiService.getResponse(url);

        final data = response['data'] as Map<String, dynamic>?;

        final setting = data ?? {};

        if (setting.isEmpty) {
          return SettingModel.empty();
        }

        final userData = SettingModel.fromJson(setting);

        return userData;
      },
    );
  }
}
