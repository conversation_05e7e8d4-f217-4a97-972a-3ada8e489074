import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/setting_controller.dart';
import '../repositories/setting_repository.dart';

// * Setting Repo Provider ========================================
final settingRepoProvider = Provider<SettingRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return SettingRepository(networkApiService: networkApiService);
});

// * Setting Change Notifier Provider ========================================
final settingControllerNotifierProvider =
    ChangeNotifierProvider<SettingController>(
  (ref) {
    final settingRepo = ref.watch(settingRepoProvider);

    return SettingController(
      settingRepo: settingRepo,
    );
  },
);

// * Setting Provider ========================================
final settingControllerProvider = Provider<SettingController>(
  (ref) {
    final settingRepo = ref.watch(settingRepoProvider);

    return SettingController(
      settingRepo: settingRepo,
    );
  },
);

// * Get Setting Future Provider ========================================
final getCitiesFutureProvider = FutureProvider(
  (
    ref,
  ) {
    final settingController = ref.watch(settingControllerProvider);

    return settingController.getSetting();
  },
);
