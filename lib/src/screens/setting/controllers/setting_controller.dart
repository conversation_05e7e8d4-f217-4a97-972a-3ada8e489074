import 'package:barber_app/src/screens/setting/models/setting_model.dart';
import 'package:barber_app/src/screens/setting/repositories/setting_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class SettingController extends BaseController {
  final SettingRepository settingRepo;

  SettingController({
    required this.settingRepo,
  });

  SettingModel setting = SettingModel.empty();

  // * Get Setting
  Future<SettingModel> getSetting() async {
    return await baseControllerFunction(
      () async {
        setting = await settingRepo.getSetting();

        return setting;
      },
    );
  }
}
