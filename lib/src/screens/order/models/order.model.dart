import 'package:barber_app/src/screens/auth/models/provider_model.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:barber_app/src/screens/cart/models/promo_code.model.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

enum OrderStatusEnum {
  pending,
  confirmed,
  delivering,
  done,
  cancelled,
}

class OrderModel {
  final int? id;
  final num deliveryCost;
  final num totalPrice;
  final num discount;
  final String address;
  final String phone;
  final String note;
  final String pageRequestUid;
  final DateTime? date;
  final PromoCodeModel? promoCode;
  final String orderNumber;
  final UserModel? user;
  final LatLng? location;
  final ProviderModel? provider;
  final List<ProductQuantityModel>? productsQuantity;
  final OrderStatusEnum status;
  final bool isService;
  final num paidOnline;

  const OrderModel({
    this.id,
    this.deliveryCost = 0,
    this.totalPrice = 0,
    this.discount = 0,
    this.paidOnline = 0,
    this.address = '',
    this.phone = '',
    this.note = '',
    this.date,
    this.orderNumber = '',
    this.user,
    this.promoCode,
    this.productsQuantity,
    this.pageRequestUid = '',
    this.provider,
    this.location,
    this.isService = false,
    this.status = OrderStatusEnum.pending,
  });

  // ar status
  String get statusTextAr {
    switch (status) {
      case OrderStatusEnum.pending:
        return 'قيد الانتظار';
      case OrderStatusEnum.confirmed:
        return 'تم تأكيد الطلب';
      case OrderStatusEnum.delivering:
        return 'جاري التوصيل';
      case OrderStatusEnum.done:
        return 'تم التوصيل';
      case OrderStatusEnum.cancelled:
        return 'تم الالغاء';
    }
  }

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'],
      deliveryCost: json['delivery_cost'] ?? 1,
      totalPrice: json['total'] ?? 0,
      discount: json['discount'] ?? 0,
      address: json['address'] ?? '',
      phone: json['phone'] ?? '',
      note: json['note'] ?? '',
      date: json['date'] == null ? null : DateTime.parse(json['date']),
      orderNumber: json['order_number']?.toString() ?? '',
      user: json['user'] == null ? null : UserModel.fromJson(json['user']),
      productsQuantity: (json['product_quantity'] as List<dynamic>?)
              ?.map((product) => ProductQuantityModel.fromJson(product))
              .toList() ??
          [],
      promoCode: json['promo_code'] == null
          ? null
          : PromoCodeModel.fromJson(json['promo_code']),
      status: json['order_status'] == null
          ? OrderStatusEnum.pending
          : convertStringToEnum(json['order_status']),
      isService: json['is_service'] ?? false,
      provider: json['provider'] == null
          ? null
          : ProviderModel.fromJson(json['provider']),
      pageRequestUid: json['page_request_uid'] ?? '',
      paidOnline: json['paid_online'] ?? 0,
      location: json['lat'] != null && json['lng'] != null
          ? LatLng(
              double.tryParse(json['lat'].toString()) ?? 0,
              double.tryParse(json['lng'].toString()) ?? 0,
            )
          : null,
    );
  }

  static OrderStatusEnum convertStringToEnum(String status) {
    switch (status) {
      case 'pending':
        return OrderStatusEnum.pending;
      case 'confirmed':
        return OrderStatusEnum.confirmed;
      case 'delivering':
        return OrderStatusEnum.delivering;
      case 'done':
        return OrderStatusEnum.done;
      case 'cancelled':
        return OrderStatusEnum.cancelled;
      default:
        return OrderStatusEnum.pending;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'order_number': orderNumber,
      'delivery_cost': deliveryCost,
      'total': totalPrice,
      'discount': discount,
      'address': address,
      'phone': phone,
      'note': note,
      'date': date?.toIso8601String(),
      'user': user?.id ?? UserModel.currentUser.id,
      if (provider != null) 'provider': provider?.id,
      if (productsQuantity != null && productsQuantity!.isNotEmpty)
        'product_quantity':
            productsQuantity?.map((product) => product.toJson()).toList(),
      if (promoCode != null && promoCode?.id != null && promoCode?.id != 0)
        'promo_code': promoCode?.id,
      'order_status': status.name,
      'is_service': isService,
      'lat': location?.latitude.toString(),
      'lng': location?.longitude.toString(),
      'page_request_uid': pageRequestUid.split('/').last ?? '',
      'paid_online': paidOnline,
    };
  }

  //? Copy with method
  OrderModel copyWith({
    int? id,
    num? deliveryCost,
    num? totalPrice,
    num? discount,
    num? paidAmount,
    String? address,
    String? phone,
    String? note,
    DateTime? date,
    String? orderNumber,
    String? pageRequestUid,
    UserModel? user,
    List<ProductQuantityModel>? productsQuantity,
    PromoCodeModel? promoCode,
    OrderStatusEnum? status,
    bool? isService,
    LatLng? location,
    ProviderModel? provider,
  }) {
    return OrderModel(
      id: id ?? this.id,
      deliveryCost: deliveryCost ?? this.deliveryCost,
      totalPrice: totalPrice ?? this.totalPrice,
      discount: discount ?? this.discount,
      paidOnline: paidAmount ?? this.paidOnline,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      note: note ?? this.note,
      date: date ?? this.date,
      orderNumber: orderNumber ?? this.orderNumber,
      user: user ?? this.user,
      productsQuantity: productsQuantity ?? this.productsQuantity,
      promoCode: promoCode ?? this.promoCode,
      status: status ?? this.status,
      isService: isService ?? this.isService,
      location: location ?? this.location,
      provider: provider ?? this.provider,
      pageRequestUid: pageRequestUid ?? this.pageRequestUid,
    );
  }
}

class ProductQuantityModel {
  final ProductModel? product;
  final int quantity;
  final num price;
  final num totalPrice;

  const ProductQuantityModel({
    this.quantity = 1,
    this.totalPrice = 0,
    this.product,
    this.price = 0,
  });

  factory ProductQuantityModel.fromJson(Map<String, dynamic> json) {
    return ProductQuantityModel(
      quantity: json['quantity'] ?? 1,
      price: json['price'] ?? 0,
      totalPrice: json['total'] ?? 0,
      product: json['product'] == null
          ? null
          : ProductModel.fromJson(json['product']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'quantity': quantity,
      'total': totalPrice,
      'price': price,
      'product': product?.id,
      'product_name': product?.name,
    };
  }

  factory ProductQuantityModel.empty() {
    return ProductQuantityModel(
      product: ProductModel.empty(),
      quantity: 1,
      totalPrice: 0,
      price: 0,
    );
  }
}

// talb 7aly or lly gaia
// view people free on that day too if lly gay sort by location lat long
//
