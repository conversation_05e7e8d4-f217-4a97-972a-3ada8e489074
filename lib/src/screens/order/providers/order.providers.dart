import 'package:barber_app/src/core/shared/providers/network_api_service_provider.dart';
import 'package:barber_app/src/screens/order/controllers/order.controller.dart';
import 'package:barber_app/src/screens/order/repositories/order.repository.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../models/order.model.dart';

// * Order Repo Provider ========================================
final orderRepoProvider = Provider<OrderRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return OrderRepository(networkApiService: networkApiService);
});

// * Order Change Notifier Provider ========================================
final orderControllerNotifierProvider = ChangeNotifierProvider<OrderController>(
  (ref) {
    final orderRepo = ref.watch(orderRepoProvider);

    return OrderController(
      orderRepo: orderRepo,
    );
  },
);

// * Order Provider ========================================
final orderControllerProvider = Provider<OrderController>(
  (ref) {
    final orderRepo = ref.watch(orderRepoProvider);

    return OrderController(
      orderRepo: orderRepo,
    );
  },
);

// * Get Order Future Provider ========================================
final getOrdersFutureProvider = FutureProvider.family<List<OrderModel>, bool>(
  (ref, isService) async {
    final orderController = ref.watch(orderControllerProvider);

    return await orderController.getOrders(isService: isService);
  },
);
