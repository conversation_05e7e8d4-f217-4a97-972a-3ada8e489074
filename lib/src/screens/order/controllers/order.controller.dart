import 'package:xr_helper/xr_helper.dart';

import '../../auth/models/user_model.dart';
import '../../payment/models/payment_models.dart';
import '../models/order.model.dart';
import '../repositories/order.repository.dart';

class OrderController extends BaseController {
  final OrderRepository orderRepo;

  OrderController({
    required this.orderRepo,
  });

  // * Get Orders
  Future<List<OrderModel>> getOrders({
    bool isService = false,
  }) async {
    return await baseControllerFunction(
      () async {
        final ordersList = await orderRepo.getOrders(
          isService: isService,
        );

        return ordersList;
      },
    );
  }

  // * Make new order
  Future<void> makeOrder(OrderModel order) async {
    return await baseControllerFunction(
      () async {
        await orderRepo.makeOrder(order);
      },
    );
  }

  // * Rate order
  Future<void> rateOrder({
    required int productId,
    required num rate,
    required String comment,
  }) async {
    return await baseControllerFunction(
      () async {
        await orderRepo.rateOrder(
          productId: productId,
          rate: rate,
          comment: comment,
        );
      },
    );
  }

  // * Create Payment Link for Order
  Future<PaymentResponse> createPaymentForOrder(
    OrderModel order, {
    String type = 'order',
    double? providerCost,
  }) async {
    return await baseControllerFunction(
      () async {
        // Extract customer information
        final user = order.user ?? UserModel.currentUser;
        final customer = PaymentCustomer(
          name: user.name,
          email: user.email,
          phone: user.phone,
        );

        // Calculate amount based on type
        double amount = 0;

        if (type == 'job' && order.isService) {
          amount = providerCost!;
        } else {
          // For regular orders, use full total price
          amount = order.totalPrice.toDouble();
        }

        // Create payment transaction
        final transaction = PaymentTransaction(
          id: order.orderNumber.isNotEmpty
              ? order.orderNumber
              : DateTime.now().millisecondsSinceEpoch.toString(),
          amount: amount,
          type: type,
          customer: customer,
          orderId: order.id?.toString(),
        );

        // Create payment link
        return await orderRepo.createPaymentLink(transaction);
      },
    );
  }
}
