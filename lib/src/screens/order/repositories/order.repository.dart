import 'dart:convert';

import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:http/http.dart' as http;
import 'package:xr_helper/xr_helper.dart';

import '../../../core/consts/app_constants.dart';
import '../../../core/consts/network/api_endpoints.dart';
import '../../payment/models/payment_models.dart';
import '../models/order.model.dart';

class OrderRepository with BaseRepository {
  final BaseApiServices networkApiService;

  OrderRepository({
    required this.networkApiService,
  });

  // * Get Orders
  Future<List<OrderModel>> getOrders({
    bool isService = false,
  }) async {
    return baseFunction(
      () async {
        final currentUserPhone = filteredPhone(
          UserModel.currentUser.phone,
          withOutCountryCode: true,
        );
        final filterByPhone = '&filters[phone][\$contains]=$currentUserPhone';

        final url =
            '${ApiEndpoints.orders}&filters[is_service]=$isService$filterByPhone';

        final response = await networkApiService.getResponse(url);

        final ordersList = response['data'] as List;

        if (ordersList.isNotEmpty) {
          final orders = ordersList.map((e) => OrderModel.fromJson(e)).toList();

          return orders;
        }

        return [];
      },
    );
  }

  // * Make new order
  Future<void> makeOrder(OrderModel order) async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.orders;

        await networkApiService.postResponse(
          url,
          body: order.toJson(),
        );

        if (order.isService) {
          NotificationService.sendNotification(
            title:
                "طلب جديد: ${order.productsQuantity?.firstOrNull?.product?.name}",
            body: " بتاريخ: ${order.date.formatDateToStringWithTime2}",
            userTokenOrTopic: order.provider?.documentId ?? '',
            isTopic: true,
          );
        }
      },
    );
  }

  // * Rate order
  Future<void> rateOrder({
    required int productId,
    required num rate,
    required String comment,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.ratings;

        await networkApiService.postResponse(
          url,
          body: {
            'rate': rate,
            'description': comment,
            'user': UserModel.currentUser.id,
            'product': productId,
          },
        );
      },
    );
  }

  // * Create Payment Link
  Future<PaymentResponse> createPaymentLink(
      PaymentTransaction transaction) async {
    return baseFunction(
      () async {
        try {
          // Prepare payment request
          final paymentRequest = PaymentRequest(
            paymentPageUid: AppConsts.payPlusPaymentPageUid,
            refUrlSuccess: AppConsts.paymentSuccessUrl(transaction.id),
            refUrlFailure: AppConsts.paymentFailureUrl(transaction.id),
            refUrlCallback: AppConsts.paymentCallbackUrl(transaction.id),
            customer: transaction.customer,
            amount: double.parse(transaction.amount.toStringAsFixed(2)),
            currencyCode: AppConsts.payPlusCurrency,
          );

          // Prepare headers
          final headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': jsonEncode({
              "api_key": AppConsts.payPlusApiKey,
              "secret_key": AppConsts.payPlusSecretKey,
            }),
          };

          // Make API call
          final url = Uri.parse(
              '${AppConsts.payPlusBaseUrl}${AppConsts.payPlusGenerateLinkEndpoint}');
          final response = await http.post(
            url,
            headers: headers,
            body: jsonEncode(paymentRequest.toJson()),
          );

          Log.w(
              'Payment_URL: ${response.request?.url}\nHeaders: ${response.request?.headers}\nBody: ${jsonEncode(paymentRequest.toJson())}\nResponse: ${response.body}');

          // Parse response
          final responseData =
              jsonDecode(response.body) as Map<String, dynamic>;

          if (response.statusCode == 200 && responseData['data'] != null) {
            final paymentUrl =
                responseData['data']['payment_page_link'] as String?;
            if (paymentUrl != null) {
              return PaymentResponse.success(paymentUrl, responseData);
            } else {
              return PaymentResponse.error('Payment URL not found in response');
            }
          } else {
            final errorMessage =
                responseData['message'] ?? 'Failed to create payment link';
            return PaymentResponse.error(errorMessage);
          }
        } catch (e) {
          return PaymentResponse.error(
              'Error creating payment link: ${e.toString()}');
        }
      },
    );
  }
}
