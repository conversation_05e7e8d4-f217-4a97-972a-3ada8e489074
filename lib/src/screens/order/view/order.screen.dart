import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:barber_app/src/screens/order/providers/order.providers.dart';
import 'package:barber_app/src/screens/order/view/widgets/order_card.widget.dart';
import 'package:barber_app/src/screens/order/view/widgets/service_order_card.widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared/widgets/lists/base_list.dart';
import '../../../core/shared/widgets/tab_bar/base_tab_bar.widget.dart';
import '../../../core/theme/color_manager.dart';
import '../models/order.model.dart';

class OrdersScreen extends HookConsumerWidget {
  const OrdersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTab = useState(0);
    final selectedSubTab = useState(0);

    final getOrdersFuture =
        ref.watch(getOrdersFutureProvider(selectedSubTab.value == 1));

    return Scaffold(
        appBar: BaseAppBar(
          title: context.tr.orders,
          showBack: false,
        ),
        body: Column(
          children: [
            BaseTabBarWidget(
              tabs: [
                context.tr.currentOrders,
                context.tr.lastOrders,
              ],
              selectedTab: selectedTab,
            ),
            BaseTabBarWidget(
              tabs: [
                context.tr.products,
                context.tr.services,
              ],
              selectedTab: selectedSubTab,
            ),
            Expanded(
              child: getOrdersFuture.get(
                data: (data) {
                  if (data.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.receipt_long,
                            size: 100,
                            color: ColorManager.lightGrey,
                          ),
                          AppGaps.gap16,
                          Text(
                            context.tr.ordersAreEmpty,
                            style: AppTextStyles.whiteSubHeadLine,
                          ),
                        ],
                      ),
                    );
                  }

                  // Filter orders based on the selected tab and isService
                  final filteredData = data.where((order) {
                    final isCurrentOrders = selectedTab.value == 0;
                    final isProductTab = selectedSubTab.value == 0;

                    final matchesStatus = isCurrentOrders
                        ? (order.status == OrderStatusEnum.pending ||
                            order.status == OrderStatusEnum.confirmed ||
                            order.status == OrderStatusEnum.delivering)
                        : (order.status == OrderStatusEnum.done ||
                            order.status == OrderStatusEnum.cancelled);

                    final matchesType =
                        isProductTab ? !order.isService : order.isService;

                    return matchesStatus && matchesType;
                  }).toList();

                  return BaseList(
                    padding: const EdgeInsets.all(AppSpaces.screenPadding),
                    data: filteredData,
                    itemBuilder: (order, index) {
                      if (selectedSubTab.value == 0) {
                        return OrderCardWidget(
                          order: order,
                        );
                      }

                      return ServiceOrderCardWidget(
                        order: order,
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ));
  }
}

//
// class OrdersScreen extends HookConsumerWidget {
//   const OrdersScreen({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final selectedTab = useState(0);
//     final selectedSubTab = useState(0);
//
//     final getOrdersFuture =
//         ref.watch(getOrdersFutureProvider(selectedSubTab.value == 1));
//
//     return Scaffold(
//         appBar: BaseAppBar(
//           title: context.tr.orders,
//           showBack: false,
//         ),
//         body: Column(
//           children: [
//             BaseTabBarWidget(
//               tabs: [
//                 context.tr.currentOrders,
//                 context.tr.lastOrders,
//               ],
//               selectedTab: selectedTab,
//             ),
//             BaseTabBarWidget(
//               tabs: [
//                 context.tr.products,
//                 context.tr.services,
//               ],
//               selectedTab: selectedSubTab,
//             ),
//             Expanded(
//               child: getOrdersFuture.get(
//                 data: (data) {
//                   if (data.isEmpty) {
//                     return Center(
//                       child: Column(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           const Icon(
//                             Icons.receipt_long,
//                             size: 100,
//                             color: ColorManager.lightGrey,
//                           ),
//                           AppGaps.gap16,
//                           Text(
//                             context.tr.ordersAreEmpty,
//                             style: AppTextStyles.whiteSubHeadLine,
//                           ),
//                         ],
//                       ),
//                     );
//                   }
//
//                   return BaseList(
//                     padding: const EdgeInsets.all(AppSpaces.screenPadding),
//                     data: data,
//                     itemBuilder: (order, index) {
//                       if (selectedSubTab.value == 0) {
//                         return OrderCardWidget(
//                           order: order,
//                         );
//                       }
//
//                       return ServiceOrderCardWidget(
//                         order: order,
//                       );
//                     },
//                   );
//                 },
//               ),
//             ),
//           ],
//         ));
//   }
// }
