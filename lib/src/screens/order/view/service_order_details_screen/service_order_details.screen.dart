import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/num_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:barber_app/src/screens/order/models/order.model.dart';
import 'package:barber_app/src/screens/order/view/widgets/service_order_card.widget.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/container/base_container.widget.dart';
import '../../../../core/theme/color_manager.dart';

class ServiceOrderDetailsScreen extends HookConsumerWidget {
  final OrderModel order;

  const ServiceOrderDetailsScreen({super.key, required this.order});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
        appBar: BaseAppBar(
          title: context.tr.orderDetails,
        ),
        body: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              ServiceOrderCardWidget(order: order),

              AppGaps.gap16,

              Text(
                context.tr.userInfo,
                style: AppTextStyles.title,
              ),

              AppGaps.gap12,

              // User Info Section
              BaseContainer(
                color: ColorManager.cardColor,
                width: double.infinity,
                radius: 16,
                padding: const EdgeInsets.all(AppSpaces.padding16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${context.tr.name}: ${order.user?.name ?? ''}',
                      style: AppTextStyles.subTitle,
                    ),
                    AppGaps.gap8,
                    Text(
                      '${context.tr.phone}: ${order.phone}',
                      style: AppTextStyles.subTitle,
                    ),
                    AppGaps.gap8,
                    Text(
                      '${context.tr.address}: ${order.address}',
                      style: AppTextStyles.subTitle,
                    ),
                  ],
                ),
              ),

              AppGaps.gap16,

              // Provider Info Section
              Text(
                context.tr.providerInfo,
                style: AppTextStyles.title,
              ),

              AppGaps.gap12,

              BaseContainer(
                color: ColorManager.cardColor,
                width: double.infinity,
                radius: 16,
                padding: const EdgeInsets.all(AppSpaces.padding16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${context.tr.name}: ${order.provider?.name ?? ''}',
                      style: AppTextStyles.subTitle,
                    ),
                    AppGaps.gap8,
                    Text(
                      '${context.tr.phone}: ${order.provider?.phone ?? ''}',
                      style: AppTextStyles.subTitle,
                    ),
                    AppGaps.gap8,
                    Text(
                      '${context.tr.address}: ${order.provider?.address ?? ''}',
                      style: AppTextStyles.subTitle,
                    ),
                  ],
                ),
              ),

              AppGaps.gap16,

              Text(
                context.tr.orderCostInfo,
                style: AppTextStyles.title,
              ),

              AppGaps.gap12,

              BaseContainer(
                color: ColorManager.cardColor,
                width: double.infinity,
                radius: 16,
                padding: const EdgeInsets.all(AppSpaces.padding16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${context.tr.serviceCost}: ${(order.productsQuantity?.fold<num>(0, (sum, item) => sum + item.totalPrice) ?? 0).toCurrency}',
                      style: AppTextStyles.subTitle,
                    ),
                    AppGaps.gap8,
                    Text(
                      '${context.tr.deliveryCost}: ${(order.deliveryCost > 0 ? order.deliveryCost : 0).toCurrency}',
                      style: AppTextStyles.subTitle,
                    ),
                    AppGaps.gap8,
                    Text(
                      '${context.tr.total}: ${order.totalPrice.toCurrency}',
                      style: AppTextStyles.subTitle,
                    ),
                  ],
                ),
              ),
            ])));
  }
}
