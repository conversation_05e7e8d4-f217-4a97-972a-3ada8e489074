import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/num_extensions.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/order/models/order.model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/container/base_container.widget.dart';
import '../service_order_details_screen/service_order_details.screen.dart';

class ServiceOrderCardWidget extends HookConsumerWidget {
  final OrderModel order;

  const ServiceOrderCardWidget({super.key, required this.order});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final totalPrice = order.totalPrice;
    // final rateCommentController = useTextEditingController();
    // final bottomNavCtrl = ref.read(bottomNavControllerProvider);

    return BaseContainer(
        onTap: () => ServiceOrderDetailsScreen(
              order: order,
            ).navigate,
        color: ColorManager.cardColor,
        radius: AppRadius.radius16,
        padding: const EdgeInsets.all(AppSpaces.padding12),
        child: Row(
          children: [
            BaseCachedImage(
              order.provider?.image?.url ?? '',
              width: 85.w,
              height: 100.h,
              radius: AppRadius.radius16,
            ),
            AppGaps.gap12,
            Expanded(
              child: SizedBox(
                height: 120.h,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          order.productsQuantity?.firstOrNull?.product?.name ??
                              '',
                          style: AppTextStyles.title,
                        ).paddingOnly(top: AppSpaces.padding8),
                        AppGaps.gap4,
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '#${order.orderNumber}',
                              style: AppTextStyles.title,
                            ),
                            Text(
                              order.date
                                      ?.toLocal()
                                      .formatDateToStringWithTime ??
                                  DateTime.now().formatDateToStringWithTime,
                              style: AppTextStyles.labelLarge.copyWith(
                                color: ColorManager.lightWhite,
                                fontSize: 13,
                              ),
                              textDirection: TextDirection.ltr,
                            ),
                          ],
                        ),
                      ],
                    ),
                    // const Spacer(),
                    // if (order.status == OrderStatusEnum.confirmed)
                    //   BaseContainer(
                    //     onTap: () {
                    //       showDialog(
                    //           context: context,
                    //           barrierDismissible: true,
                    //           builder: (BuildContext cont) => SlideRatingDialog(
                    //                 title: context.tr.rate,
                    //                 subTitle: context.tr.howWouldYouRate,
                    //                 buttonTitle: context.tr.save,
                    //                 buttonColor: ColorManager.primaryColor,
                    //                 rateCommentController:
                    //                     rateCommentController,
                    //                 hintText:
                    //                     '${context.tr.writeYourComment}...',
                    //                 onRatingChanged: (rating) {
                    //                   print(rating.toString());
                    //                 },
                    //                 buttonOnTap: () {
                    //                   bottomNavCtrl.changeIndex(0);
                    //
                    //                   showToast(
                    //                     context.tr.thankYouForYourRating,
                    //                   );
                    //
                    //                   const MainScreen().navigateReplacement;
                    //                   // Do your Business Logic here;
                    //                 },
                    //               ));
                    //     },
                    //     padding: const EdgeInsets.symmetric(
                    //       horizontal: AppSpaces.padding12,
                    //       vertical: AppSpaces.padding4,
                    //     ),
                    //     radius: AppRadius.radius8,
                    //     borderWidth: 2,
                    //     borderColor: ColorManager.primaryColor.withOpacity(0.1),
                    //     color: Colors.transparent,
                    //     child: Text(
                    //       context.tr.rateOrder,
                    //       style: AppTextStyles.labelLarge.copyWith(
                    //         color: ColorManager.primaryColor,
                    //       ),
                    //     ).paddingOnly(bottom: AppSpaces.padding4 - 2),
                    //   ),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${context.tr.total}: ${totalPrice.toCurrency}',
                          style: AppTextStyles.subTitle
                              .copyWith(color: ColorManager.lightWhite),
                        ),
                        const Spacer(),
                        BaseContainer(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSpaces.padding8,
                            vertical: AppSpaces.padding8,
                          ),
                          color: order.status == OrderStatusEnum.confirmed
                              ? ColorManager.successColor.withOpacity(0.1)
                              : order.status == OrderStatusEnum.cancelled
                                  ? ColorManager.errorColor.withOpacity(0.1)
                                  : order.status == OrderStatusEnum.pending
                                      ? ColorManager.primaryColor
                                          .withOpacity(0.1)
                                      : ColorManager.primaryColor
                                          .withOpacity(0.1),
                          child: Text(
                            order.statusTextAr,
                            style: AppTextStyles.labelLarge.copyWith(
                              color: order.status == OrderStatusEnum.confirmed
                                  ? ColorManager.successColor
                                  : order.status == OrderStatusEnum.cancelled
                                      ? ColorManager.errorColor
                                      : order.status == OrderStatusEnum.pending
                                          ? ColorManager.primaryColor
                                          : ColorManager.primaryColor,
                              // fontSize: 13,
                            ),
                          ).paddingOnly(bottom: AppSpaces.padding4 - 2),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ));
  }
}
