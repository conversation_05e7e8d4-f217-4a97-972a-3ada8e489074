import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/num_extensions.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/main_screen/view/main.screen.dart';
import 'package:barber_app/src/screens/order/models/order.model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:slide_rating_dialog/slide_rating_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/container/base_container.widget.dart';
import '../../../../core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import '../../../auth/models/user_model.dart';
import '../../../product/providers/product.providers.dart';
import '../../providers/order.providers.dart';

class OrderCardWidget extends HookConsumerWidget {
  final OrderModel order;

  const OrderCardWidget({super.key, required this.order});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final totalPrice = order.totalPrice;
    final productCount = order.productsQuantity?.length ?? 0;
    final orderController = ref.read(orderControllerNotifierProvider);

    final rateCommentController = useTextEditingController();
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);
    final rateValueNotifier = useState<num>(0.0);

    final userCanRate = order.status == OrderStatusEnum.confirmed &&
        order.productsQuantity?.every((productQuantity) {
              final product = productQuantity.product;
              return product?.ratings.any(
                      (rating) => rating.user.id == UserModel.currentUser.id) ==
                  false;
            }) ==
            true;

    return BaseContainer(
      color: ColorManager.cardColor,
      radius: AppRadius.radius16,
      padding: EdgeInsets.zero,
      child: ExpansionTile(
        backgroundColor: Colors.transparent,
        collapsedBackgroundColor: Colors.transparent,
        iconColor: ColorManager.lightWhite,
        collapsedIconColor: ColorManager.lightWhite,
        tilePadding: const EdgeInsets.symmetric(
          horizontal: AppSpaces.padding16,
          vertical: AppSpaces.padding12,
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '#${order.orderNumber}',
                  style: AppTextStyles.title,
                ),
                if (order.status == OrderStatusEnum.confirmed && userCanRate)
                  BaseContainer(
                    onTap: () {
                      showDialog(
                          context: context,
                          barrierDismissible: true,
                          builder: (BuildContext cont) => SlideRatingDialog(
                                title: context.tr.rate,
                                subTitle: context.tr.howWouldYouRate,
                                buttonTitle: context.tr.save,
                                buttonColor: ColorManager.primaryColor,
                                rateCommentController: rateCommentController,
                                hintText: '${context.tr.writeYourComment}...',
                                onRatingChanged: (rating) {
                                  rateValueNotifier.value = rating;
                                },
                                buttonOnTap: () async {
                                  await Future.forEach(
                                      order.productsQuantity ??
                                          <ProductQuantityModel>[],
                                      (productQuantity) async {
                                    final product = productQuantity.product;
                                    if (product != null) {
                                      await orderController.rateOrder(
                                        productId: product.id!,
                                        rate: rateValueNotifier.value,
                                        comment: rateCommentController.text,
                                      );
                                    }
                                  });

                                  bottomNavCtrl.changeIndex(0);

                                  ref.invalidate(
                                      getFeaturedProductsFutureProvider);
                                  ref.invalidate(getProductsFutureProvider);
                                  ref.invalidate(getOrdersFutureProvider);

                                  showToast(
                                    context.tr.thankYouForYourRating,
                                  );

                                  const MainScreen().navigateReplacement;
                                  // Do your Business Logic here;
                                },
                              ));
                    },
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.padding12,
                      vertical: AppSpaces.padding4,
                    ),
                    radius: AppRadius.radius8,
                    borderWidth: 2,
                    borderColor: ColorManager.primaryColor.withOpacity(0.1),
                    color: Colors.transparent,
                    child: Text(
                      context.tr.rateOrder,
                      style: AppTextStyles.labelLarge.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ).paddingOnly(bottom: AppSpaces.padding4 - 2),
                  ),
                Text(
                  '${order.date?.toLocal().formatDateToStringWithTime}',
                  style: AppTextStyles.labelLarge.copyWith(
                    color: ColorManager.lightWhite,
                    fontSize: 13,
                  ),
                  textDirection: TextDirection.ltr,
                ),
              ],
            ),
            AppGaps.gap4,
            Text(
              '${context.tr.products}: $productCount',
              style: AppTextStyles.subTitle
                  .copyWith(color: ColorManager.lightWhite),
            ),
            if (order.discount > 0) ...[
              AppGaps.gap4,
              Text(
                '${context.tr.discount}: ${order.discount.toCurrency}',
                style: AppTextStyles.subTitle
                    .copyWith(color: ColorManager.lightWhite),
              ),
            ],
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${context.tr.total}: ${totalPrice.toCurrency}',
                  style: AppTextStyles.subTitle
                      .copyWith(color: ColorManager.lightWhite),
                ),
                const Spacer(),
                BaseContainer(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.padding8,
                    vertical: AppSpaces.padding8,
                  ),
                  color: order.status == OrderStatusEnum.confirmed
                      ? ColorManager.successColor.withOpacity(0.1)
                      : order.status == OrderStatusEnum.cancelled
                          ? ColorManager.errorColor.withOpacity(0.1)
                          : order.status == OrderStatusEnum.pending
                              ? ColorManager.primaryColor.withOpacity(0.1)
                              : ColorManager.primaryColor.withOpacity(0.1),
                  child: Text(
                    order.statusTextAr,
                    style: AppTextStyles.labelLarge.copyWith(
                      color: order.status == OrderStatusEnum.confirmed
                          ? ColorManager.successColor
                          : order.status == OrderStatusEnum.cancelled
                              ? ColorManager.errorColor
                              : order.status == OrderStatusEnum.pending
                                  ? ColorManager.primaryColor
                                  : ColorManager.primaryColor,
                      // fontSize: 13,
                    ),
                  ).paddingOnly(bottom: AppSpaces.padding4 - 2),
                ),
              ],
            ),
          ],
        ),
        children: order.productsQuantity?.map((productQuantity) {
              final product = productQuantity.product;
              return ListTile(
                title: Text(
                  product?.name ?? '',
                  style: AppTextStyles.subTitle,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppGaps.gap4,
                    Text(
                      '${context.tr.price}: ${(productQuantity.price).toCurrency}',
                      style: AppTextStyles.subTitle
                          .copyWith(color: ColorManager.lightWhite),
                    ),
                    Text(
                      '${context.tr.quantity}: ${productQuantity.quantity}',
                      style: AppTextStyles.subTitle
                          .copyWith(color: ColorManager.lightWhite),
                    ),
                    Text(
                      '${context.tr.total}: ${(productQuantity.price * productQuantity.quantity).toCurrency}',
                      style: AppTextStyles.subTitle
                          .copyWith(color: ColorManager.lightWhite),
                    ),
                  ],
                ),
                trailing: BaseCachedImage(
                  product?.thumbnail.url ?? '',
                  width: 50.w,
                  height: 50.h,
                  radius: AppRadius.radius8,
                ),
              );
            }).toList() ??
            [],
      ),
    );
  }
}
