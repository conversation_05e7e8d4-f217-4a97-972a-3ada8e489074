import 'package:barber_app/src/core/shared/models/base_media.model.dart';

import '../../auth/models/user_model.dart';
import '../../category/models/category.model.dart';

class ProductModel {
  final int? id;
  final String documentId;
  final String name;
  final int sessionTime;
  final String description;
  final num price;
  final num salePrice;
  final DateTime? createdAt;
  final int stock;
  final bool featured;
  final bool? isService;
  final List<BaseMediaModel> images;
  final CategoryModel? category;
  final List<RatingModel> ratings;

  const ProductModel({
    this.id,
    this.documentId = '',
    this.name = '',
    this.description = '',
    this.price = 0,
    this.salePrice = 0,
    this.sessionTime = 0,
    this.createdAt,
    this.stock = 0,
    this.featured = false,
    this.isService,
    this.images = const [],
    this.ratings = const [],
    this.category,
  });

  BaseMediaModel get thumbnail {
    return images.isNotEmpty ? images.first : const BaseMediaModel();
  }

  List<String> get imageUrls {
    return images.map((image) => image.url).toList();
  }

  num get actualPrice => salePrice > 0 ? salePrice : price;

  // calculate the average rating
  num get averageRating {
    if (ratings.isEmpty) return 0;
    num total = 0;
    for (var rating in ratings) {
      total += rating.rate;
    }
    return total / ratings.length;
  }

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      id: json['id'],
      documentId: json['documentId'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: json['price'] ?? 0,
      salePrice: json['sale_price'] ?? 0,
      createdAt:
          json['createdAt'] == null ? null : DateTime.parse(json['createdAt']),
      stock: json['stock'] ?? 0,
      featured: json['featured'] ?? false,
      isService: json['is_service'],
      images: (json['images'] as List<dynamic>?)
              ?.map((image) => BaseMediaModel.fromJson(image))
              .toList() ??
          [],
      category: json['category'] != null
          ? CategoryModel.fromJson(json['category'])
          : CategoryModel.empty(),
      sessionTime: json['session_time'] ?? 0,
      ratings: (json['ratings'] as List<dynamic>?)
              ?.map((rating) => RatingModel.fromJson(rating))
              .toList() ??
          [],
    );
  }

  factory ProductModel.empty() {
    return ProductModel(
      id: 0,
      documentId: '',
      name: '',
      description: '',
      price: 0,
      salePrice: 0,
      createdAt: DateTime.now(),
      stock: 0,
      featured: false,
      isService: false,
      images: [],
      sessionTime: 0,
      category: CategoryModel.empty(),
      ratings: [],
    );
  }

  // toJson method
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'documentId': documentId,
      'name': name,
      'description': description,
      'price': price,
      'sale_price': salePrice,
      'createdAt': createdAt?.toIso8601String(),
      'stock': stock,
      'featured': featured,
      'isService': isService,
      'images': images.map((image) => image.toJson()).toList(),
      'category': category?.toJson(),
      'session_time': sessionTime,
      // 'ratings': ratings.map((rating) => rating.toJson()).toList(),
    };
  }
}

class RatingModel {
  final num rate;
  final String description;
  final UserModel user;
  final ProductModel product;

  const RatingModel({
    this.rate = 0,
    this.description = '',
    required this.user,
    required this.product,
  });

  factory RatingModel.fromJson(Map<String, dynamic> json) {
    return RatingModel(
      rate: json['rate'] ?? 0,
      description: json['description'] ?? '',
      user: json['user'] == null
          ? UserModel.empty()
          : UserModel.fromJson(json['user']),
      product: json['product'] == null
          ? ProductModel.empty()
          : ProductModel.fromJson(json['product']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'rate': rate,
      'description': description,
      'user': user.id,
      'product': product.id,
    };
  }
}
