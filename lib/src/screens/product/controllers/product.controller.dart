import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:barber_app/src/screens/product/repositories/product.repository.dart';
import 'package:xr_helper/xr_helper.dart';

class ProductController extends BaseController {
  final ProductRepository productRepo;

  ProductController({
    required this.productRepo,
  });

  // * Get Products
  Future<List<ProductModel>> getProducts({
    bool isService = false,
    bool? isFeatured,
  }) async {
    return await baseControllerFunction(
      () async {
        return await productRepo.getProducts(
            isService: isService, isFeatured: isFeatured);
      },
    );
  }

  // * Add Products
  Future<void> addProduct({
    required Map<String, dynamic> data,
  }) async {
    return await baseControllerFunction(
      () async {
        return await productRepo.addProducts(data: data);
      },
    );
  }
}
