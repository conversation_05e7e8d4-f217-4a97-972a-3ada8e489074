import 'package:barber_app/src/core/shared/providers/network_api_service_provider.dart';
import 'package:barber_app/src/screens/product/controllers/product.controller.dart';
import 'package:barber_app/src/screens/product/repositories/product.repository.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// * Products Repo Provider ========================================
final productsRepoProvider = Provider<ProductRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return ProductRepository(networkApiService: networkApiService);
});

// * Products Change Notifier Provider ========================================
final productsControllerNotifierProvider =
    ChangeNotifierProvider<ProductController>(
  (ref) {
    final productsRepo = ref.watch(productsRepoProvider);

    return ProductController(
      productRepo: productsRepo,
    );
  },
);

// * Products Provider ========================================
final productsControllerProvider = Provider<ProductController>(
  (ref) {
    final productsRepo = ref.watch(productsRepoProvider);

    return ProductController(
      productRepo: productsRepo,
    );
  },
);

// * Get Products Future Provider ========================================
final getProductsFutureProvider = FutureProvider(
  (ref) {
    final productsController = ref.watch(productsControllerProvider);

    return productsController.getProducts();
  },
);

// * Get Featured Products Future Provider ========================================
final getFeaturedProductsFutureProvider = FutureProvider(
  (ref) {
    final productsController = ref.watch(productsControllerProvider);

    return productsController.getProducts(isFeatured: true);
  },
);

// * Get Services Future Provider ========================================
final getServicesFutureProvider = FutureProvider(
  (ref) {
    final productsController = ref.watch(productsControllerProvider);

    return productsController.getProducts(isService: true);
  },
);
