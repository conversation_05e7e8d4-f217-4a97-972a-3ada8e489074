import 'package:barber_app/src/core/consts/network/api_endpoints.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:xr_helper/xr_helper.dart';

class ProductRepository with BaseRepository {
  final BaseApiServices networkApiService;

  ProductRepository({
    required this.networkApiService,
  });

  // * Get Products
  Future<List<ProductModel>> getProducts({
    bool isService = false,
    bool? isFeatured,
  }) async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.filterProducts(
          isService: isService,
          isFeatured: isFeatured,
        );

        final response = await networkApiService.getResponse(url);

        final products = response['data'] as List;

        products.sort((a, b) {
          final aSort = (a['sort'] as int?) ?? 0;
          final bSort = (b['sort'] as int?) ?? 0;
          return aSort.compareTo(bSort);
        });

        final productsList =
            products.map((data) => ProductModel.fromJson(data)).toList();

        return productsList;
      },
    );
  }

  // * Add Products
  Future<void> addProducts({
    required Map<String, dynamic> data,
  }) async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.products;

        await networkApiService.postResponse(
          url,
          body: data,
        );
      },
    );
  }
}
