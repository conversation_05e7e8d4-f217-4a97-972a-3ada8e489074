import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:barber_app/src/screens/product/view/product_details_screen/widgets/product_bottom_nav.widget.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/app_bar/base_appbar.dart';
import '../../../../core/shared/widgets/banner/base_banner.widget.dart';
import '../../../../core/theme/color_manager.dart';

class ProductDetailsScreen extends StatelessWidget {
  final ProductModel product;

  const ProductDetailsScreen({
    super.key,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const BaseAppBar(),
      bottomNavigationBar: ProductBottomNavWidget(
        product: product,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpaces.screenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // * Product Banner Widget
            BaseBannerWidget(
              images: product.imageUrls,
            ),

            AppGaps.gap24,

            // * Product Name
            Text(
              product.name,
              style: AppTextStyles.title,
            ),

            AppGaps.gap24,

            // * Category Name
            Row(
              children: [
                Flexible(
                  child: Text(
                    "${context.tr.from}: ${product.category?.name ?? ''}",
                    style: AppTextStyles.subTitle,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // if (product.category?.image?.url != null) ...[
                //   AppGaps.gap12,
                //   product.category!.image!.url.networkImage(
                //     height: 40.h,
                //     fit: BoxFit.cover,
                //   )
                // ],
              ],
            ),

            AppGaps.gap24,

            Text(
              '${context.tr.description}:',
              style: AppTextStyles.title,
            ),

            AppGaps.gap24,

            Text(
              product.description,
              style: AppTextStyles.subTitle,
            ),

            AppGaps.gap24,

            Text(
              '${context.tr.rate}:',
              style: AppTextStyles.title,
            ),

            AppGaps.gap24,

            Row(
              children: [
                const Icon(
                  Icons.star,
                  color: ColorManager.lightWhite,
                  size: 16,
                ),
                AppGaps.gap8,
                Text(product.averageRating.toStringAsFixed(1),
                    style: AppTextStyles.subTitle
                        .copyWith(color: ColorManager.lightWhite)),
                AppGaps.gap8,
                Text('(+${product.ratings.length})',
                    style: AppTextStyles.subTitle
                        .copyWith(color: ColorManager.lightWhite)),
              ],
            ),

            AppGaps.gap24,
          ],
        ),
      ),
    );
  }
}
