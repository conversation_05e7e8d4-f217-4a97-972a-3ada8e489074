import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/num_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/string_extensions.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../generated/assets.gen.dart';
import '../../../../../core/shared/widgets/cart_widgets/increase_decrease.widget.dart';
import '../../../../cart/providers/cart.providers.dart';

class ProductBottomNavWidget extends HookConsumerWidget {
  final ProductModel product;

  const ProductBottomNavWidget({super.key, required this.product});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartController = ref.read(cartControllerNotifierProvider);

    final quantity = useState(1);

    return Container(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        decoration: const BoxDecoration(
          color: ColorManager.black,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppRadius.radius20),
            topRight: Radius.circular(AppRadius.radius20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // * Quantity
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${context.tr.quantity}:',
                  style: AppTextStyles.title,
                ),
                IncreaseDecreaseWidget(
                  quantity: quantity,
                )
              ],
            ),

            // * Product Price
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${context.tr.productPrice}:',
                  style: AppTextStyles.subTitle,
                ),
                Text(
                  product.actualPrice.toCurrency,
                  style: AppTextStyles.subTitle,
                ).paddingSymmetric(horizontal: AppSpaces.padding12),
              ],
            ),

            // AppGaps.gap12,
            //
            // // * Delivery Cost
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     Text(
            //       '${context.tr.deliveryCost}:',
            //       style: AppTextStyles.subTitle,
            //     ),
            //     Text(
            //       '${UserModel.userCity.deliveryCost}'.toCurrency,
            //       style: AppTextStyles.subTitle,
            //     ).paddingSymmetric(horizontal: AppSpaces.padding12),
            //   ],
            // ),
            //
            // AppGaps.gap12,
            //
            // // * Time for Product Delivery
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     Text(
            //       context.tr.timeForProductDelivery,
            //       style: AppTextStyles.subTitle,
            //     ),
            //     Row(
            //       children: [
            //         Text(
            //           UserModel.userCity.deliveryTime.toString(),
            //           style: AppTextStyles.subTitle,
            //         ),
            //         AppGaps.gap4,
            //         const Icon(
            //           Icons.access_time,
            //           color: ColorManager.white,
            //           size: 18,
            //         ),
            //       ],
            //     ).paddingSymmetric(horizontal: AppSpaces.padding12),
            //   ],
            // ),

            AppGaps.gap12,

            // * Total Price
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${context.tr.totalPrice}:',
                  style: AppTextStyles.title,
                ),
                Text(
                  '${(product.actualPrice * quantity.value)}'.toCurrency,
                  style: AppTextStyles.subTitle,
                ).paddingSymmetric(horizontal: AppSpaces.padding12),
              ],
            ),

            AppGaps.gap16,

            Button(
              label: context.tr.addToCart,
              textColor: Colors.black,
              isPrefixIcon: true,
              icon: Assets.icons.addToCart.image(
                height: 20.h,
              ),
              onPressed: () {
                if (!UserModel.isLogged()) {
                  showToast(
                    context.tr.loginFirstPlease,
                    isError: true,
                  );
                  return;
                }

                showToast(context.tr.addedSuccessfully);

                cartController.updateCart(
                  product: product,
                  quantity: quantity.value,
                );

                ref.invalidate(getCartFutureProvider);
              },
            )
          ],
        ));
  }
}
