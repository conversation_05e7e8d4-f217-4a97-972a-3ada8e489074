// import 'package:barber_app/src/core/theme/color_manager.dart';
// import 'package:carousel_slider/carousel_slider.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class ProductBannerWidget extends HookConsumerWidget {
//   final List<String> images;
//
//   const ProductBannerWidget({
//     super.key,
//     required this.images,
//   });
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final sliderIndex = useState(0);
//
//     return Column(
//       children: [
//         CarouselSlider(
//           items: images
//               .map((e) => Padding(
//                     padding: const EdgeInsets.symmetric(
//                         horizontal: AppSpaces.padding8),
//                     child: BaseCachedImage(
//                       e,
//                       height: 160.h,
//                       width: double.infinity,
//                       fit: BoxFit.cover,
//                       radius: AppRadius.radius16,
//                       showErrorWidget: e.isNotEmpty,
//                     ),
//                   ))
//               .toList(),
//           options: CarouselOptions(
//             height: 160.h,
//             onPageChanged: (index, reason) {
//               sliderIndex.value = index;
//             },
//             viewportFraction: 1.0,
//             initialPage: 0,
//             enableInfiniteScroll: true,
//             autoPlayInterval: const Duration(seconds: 3),
//             autoPlayAnimationDuration: const Duration(milliseconds: 800),
//             autoPlayCurve: Curves.fastOutSlowIn,
//             enlargeCenterPage: false,
//             scrollDirection: Axis.horizontal,
//           ),
//         ),
//         AppGaps.gap12,
//         Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: images
//               .asMap()
//               .entries
//               .map((e) => Container(
//                     width: e.key == sliderIndex.value ? 30 : 15.w,
//                     height: 8.0,
//                     margin: const EdgeInsets.symmetric(
//                         horizontal: AppSpaces.padding4),
//                     decoration: BoxDecoration(
//                       borderRadius: BorderRadius.circular(100),
//                       color: e.key == sliderIndex.value
//                           ? ColorManager.primaryColor
//                           : ColorManager.lightGrey,
//                     ),
//                   ))
//               .toList(),
//         )
//       ],
//     );
//   }
// }
