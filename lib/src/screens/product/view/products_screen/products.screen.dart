import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:barber_app/src/core/shared/widgets/tab_bar/base_tab_bar.widget.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/category/models/sub_category.model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../category/providers/category.providers.dart';
import '../../../category/view/widgets/sub_categories_list.widget.dart';
import '../../../home/<USER>/widgets/products_list.widget.dart';
import '../../../home/<USER>/widgets/services_list.widget.dart';

class ProductsScreen extends HookConsumerWidget {
  const ProductsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final subCategoriesFuture = ref.watch(getSubCategoriesFutureProvider);

    final selectedSubCat = useState<SubCategoryModel?>(null);
    final selectedTab = useState<int>(0);

    final searchValue = useState('');

    final searchedProducts = useState(selectedSubCat.value?.products ?? []);

    if (searchValue.value.isEmpty) {
      searchedProducts.value = selectedSubCat.value?.products ?? [];
    } else {
      searchedProducts.value = selectedSubCat.value?.products
              ?.where((product) =>
                  product.name.toLowerCase().contains(searchValue.value))
              .toList() ??
          [];
    }

    return Scaffold(
      appBar: BaseAppBar(
        title: context.tr.products,
      ),
      body: Padding(
        padding:
            const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),
        child: ListView(
          children: [
            AppGaps.gap24,

            // * Search Bar
            Container(
              height: 45,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                  border: Border.all(
                    color: ColorManager.primaryColor,
                    width: 2,
                  )),
              child: TextField(
                onChanged: (value) {
                  searchValue.value = value;
                },
                style: const TextStyle(
                  color: ColorManager.white,
                ),
                decoration: InputDecoration(
                    hintText: context.tr.search,
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    hintStyle: const TextStyle(
                      color: ColorManager.white,
                    ),
                    contentPadding: const EdgeInsets.only(
                      right: AppSpaces.padding16,
                      top: AppSpaces.padding4,
                    ),
                    suffixIcon: Container(
                      decoration: BoxDecoration(
                        color: ColorManager.primaryColor,
                        borderRadius: BorderRadius.circular(AppRadius.radius4),
                      ),
                      padding: const EdgeInsets.all(AppSpaces.padding8),
                      child: const Icon(
                        CupertinoIcons.search,
                        color: Colors.black,
                      ),
                    )),
              ),
            ),

            AppGaps.gap24,

            BaseTabBarWidget(
              tabs: [
                context.tr.products,
                context.tr.services,
              ],
              selectedTab: selectedTab,
            ),

            // * Categories
            AppGaps.gap16,

            if (selectedTab.value == 0) ...[
              subCategoriesFuture.get(
                data: (subCategories) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (selectedSubCat.value == null) {
                      selectedSubCat.value = subCategories.firstOrNull;
                    }
                  });

                  return SubCategoriesListWidget(
                    subCategories: subCategories,
                    selectedSubCat: selectedSubCat,
                  );
                },
              ),

              AppGaps.gap16,

              // * Latest Products
              ProductsListWidget(
                title: context.tr.latestProducts,
                products: searchedProducts.value,
              ),
            ] else ...[
              // * Services
              ServicesListWidget(
                search: searchValue.value,
                isHorizontal: false,
              )
            ],
          ],
        ),
      ),
    );
  }
}
