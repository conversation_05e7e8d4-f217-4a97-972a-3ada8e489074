// import 'package:barber_app/src/core/shared/extensions/riverpod_extensions.dart';
// import 'package:barber_app/src/core/shared/widgets/lists/base_list.dart';
// import 'package:barber_app/src/core/theme/color_manager.dart';
// import 'package:barber_app/src/screens/product/view/product_card/product_card.dart';
// import 'package:barber_app/src/screens/stores/providers/store.providers.dart';
// import 'package:barber_app/src/screens/stores/view/store_details_screen/store_details.screen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import '../../../models/product.model.dart';
//
// class ProductsGridWidget extends ConsumerWidget {
//   const ProductsGridWidget({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final storesFuture = ref.watch(getStoresFutureProvider);
//
//     return BaseList.grid(
//       data: const [0, 1, 2, 3, 4],
//       crossAxisCount: 2,
//       mainAxisSpacing: AppSpaces.padding16,
//       itemBuilder: (context, index) => ProductCard(
//         product: ProductModel.empty(),
//       ),
//       separatorGap: AppGaps.gap16,
//     );
//
//     return storesFuture.get(
//       data: (stores) {
//         return BaseList(
//           data: stores,
//           isGrid: true,
//           itemBuilder: (context, index) => GestureDetector(
//             onTap: () {
//               const StoreDetailsScreen().navigate;
//             },
//             child: Stack(
//               children: [
//                 BaseCachedImage(
//                   'https://static.wixstatic.com/media/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png/v1/fill/w_614,h_614,al_c,lg_1,q_90/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png',
//                   height: 120.h,
//                   width: 220.w,
//                   radius: AppRadius.radius20,
//                 ),
//                 Container(
//                   width: 220.w,
//                   padding: const EdgeInsets.symmetric(
//                       horizontal: AppSpaces.padding12,
//                       vertical: AppSpaces.padding8),
//                   alignment: Alignment.bottomCenter,
//                   decoration: BoxDecoration(
//                       color: Colors.black.withOpacity(0.2),
//                       borderRadius: BorderRadius.circular(AppRadius.radius20)),
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Text(
//                         'Pet Store',
//                         style: AppTextStyles.whiteTitle.copyWith(
//                           fontSize: 20,
//                         ),
//                       ),
//                       const CircleAvatar(
//                         radius: 16,
//                         backgroundColor: ColorManager.primaryColor,
//                         child: Icon(
//                           Icons.arrow_forward_ios,
//                           color: Colors.white,
//                           size: 18,
//                         ),
//                       )
//                     ],
//                   ),
//                 )
//               ],
//             ),
//           ),
//           separatorGap: AppGaps.gap16,
//         );
//       },
//     );
//   }
// }
