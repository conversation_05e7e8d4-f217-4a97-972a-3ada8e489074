import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/num_extensions.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:barber_app/src/screens/cart/providers/cart.providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/container/base_container.widget.dart';
import '../../models/product.model.dart';
import '../product_details_screen/product_details.screen.dart';

class ProductCard extends ConsumerWidget {
  final ProductModel product;
  final double? height;

  const ProductCard({super.key, required this.product, this.height});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartController = ref.read(cartControllerNotifierProvider);

    final haveSale = product.salePrice != 0;

    return BaseContainer(
      onTap: () => ProductDetailsScreen(
        product: product,
      ).navigate,
      height: height,
      width: 300.w,
      color: ColorManager.cardColor,
      radius: AppRadius.radius16,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpaces.padding8,
        vertical: AppSpaces.padding8,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BaseCachedImage(
            product.thumbnail.url,
            width: 105.w,
            height: 110.h,
            radius: AppRadius.radius16,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    product.name,
                    style: AppTextStyles.subTitle,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (product.ratings.isNotEmpty) ...[
                  Expanded(
                      child: Row(
                    children: [
                      const Icon(
                        Icons.star,
                        color: ColorManager.lightWhite,
                        size: 16,
                      ),
                      AppGaps.gap8,
                      Text(product.averageRating.toStringAsFixed(1),
                          style: AppTextStyles.subTitle
                              .copyWith(color: ColorManager.lightWhite)),
                      AppGaps.gap8,
                      Text('(+${product.ratings.length})',
                          style: AppTextStyles.subTitle
                              .copyWith(color: ColorManager.lightWhite)),
                    ],
                  )),
                ],
                AppGaps.gap8,
                Expanded(
                  child: Align(
                    alignment: Alignment.bottomRight,
                    child: haveSale
                        ? FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  ' ${context.tr.price}:',
                                  style: AppTextStyles.subTitle.copyWith(
                                    color: ColorManager.lightWhite,
                                  ),
                                ),
                                Text(
                                  product.price.toCurrency,
                                  style: AppTextStyles.subTitle.copyWith(
                                      color: ColorManager.lightWhite,
                                      decoration: TextDecoration.lineThrough,
                                      decorationColor: ColorManager.grey),
                                ),
                                AppGaps.gap8,
                                Text(
                                  product.salePrice.toCurrency,
                                  style: AppTextStyles.subTitle.copyWith(
                                    color: ColorManager.lightWhite,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : Text(
                            'السعر: ${product.price} NIS',
                            style: AppTextStyles.subTitle.copyWith(
                              color: ColorManager.lightWhite,
                            ),
                          ),
                  ),
                ),
              ],
            ).paddingAll(
              AppSpaces.padding8,
            ),
          ),
          InkWell(
            onTap: () {
              if (!UserModel.isLogged()) {
                showToast(
                  context.tr.loginFirstPlease,
                  isError: true,
                );
                return;
              }

              showToast(context.tr.addedSuccessfully);

              cartController.updateCart(
                product: product,
                quantity: 1,
              );

              ref.invalidate(getCartFutureProvider);
            },
            child: CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              radius: 14.r,
              child: const Icon(
                Icons.add_shopping_cart_outlined,
                color: Colors.white,
                size: 18,
              ),
            ).paddingAll(AppSpaces.padding8),
          ),
        ],
      ),
    );
  }
}
