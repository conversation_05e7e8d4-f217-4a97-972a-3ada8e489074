import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/home/<USER>/widgets/categories_list.widget.dart';
import 'package:barber_app/src/screens/home/<USER>/widgets/home_app_bar.widget.dart';
import 'package:barber_app/src/screens/home/<USER>/widgets/home_banner.widget.dart';
import 'package:barber_app/src/screens/product/view/products_screen/products.screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/products_list.widget.dart';
import 'widgets/services_list.widget.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: ColorManager.backgroundColor,
        surfaceTintColor: ColorManager.backgroundColor,
        centerTitle: false,
        title: const HomeAppbarWidget(),
        actions: [
          IconButton(
              onPressed: () {
                const ProductsScreen().navigate;
              },
              icon: const Icon(
                CupertinoIcons.search,
                color: Colors.white,
                size: 25,
              )),
        ],
      ),
      body: ListView(
        padding:
            const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),
        children: [
          AppGaps.gap16,

          // * Home Slider
          const HomeBannerWidget(),

          AppGaps.gap16,

          // * Services
          const ServicesListWidget(),

          AppGaps.gap24,

          // * Categories
          const CategoriesListWidget(),

          AppGaps.gap24,

          // * Latest Products
          ProductsListWidget(
            title: context.tr.latestProducts,
          ),

          AppGaps.gap24,

          // * Best Sellers Products
          ProductsListWidget(
            title: context.tr.bestSellers,
            isFeatured: true,
          ),

          AppGaps.gap24,

          // AppGaps.gap24,
          //
          // // * Stores
          // HomeStoresListWidget(),
          //
          // AppGaps.gap24,
          //
          // // * Doctors
          // DoctorsListWidget(),

          // AppGaps.gap24,
        ],
      ),
    );
  }
}
