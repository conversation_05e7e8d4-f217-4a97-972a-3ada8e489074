import 'package:barber_app/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:barber_app/src/screens/product/providers/product.providers.dart';
import 'package:barber_app/src/screens/product/view/product_card/product_card.dart';
import 'package:barber_app/src/screens/product/view/products_screen/products.screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/lists/base_list.dart';
import '../../../../core/shared/widgets/loading/shimmer_loading.widget.dart';
import '../../../product/models/product.model.dart';
import 'see_all_button.widget.dart';

class ProductsListWidget extends ConsumerWidget {
  final String title;
  final bool isFeatured;
  final List<ProductModel>? products;

  const ProductsListWidget({
    super.key,
    required this.title,
    this.isFeatured = false,
    this.products,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getProductsFuture = products == null
        ? ref.watch(isFeatured
            ? getFeaturedProductsFutureProvider
            : getProductsFutureProvider)
        : null;

    return Column(
      children: [
        Row(
          children: [
            Text(title, style: AppTextStyles.title),
            const Spacer(),
            SeeAllButtonWidget(
              onPressed: () {
                const ProductsScreen().navigate;
              },
            )
          ],
        ),
        AppGaps.gap8,
        if (products == null)
          getProductsFuture!.get(
            loading: () => BaseShimmerLoading(
              child: _buildProductsList(
                  List.generate(3, (index) => ProductModel.empty())),
            ),
            data: (data) {
              return _buildProductsList(data);
            },
          )
        else
          _buildProductsList(products!),
      ],
    );
  }

  Widget _buildProductsList(List<ProductModel> products) {
    if (this.products == null) {
      return BaseList.horizontal(
        height: 100.h,
        data: products,
        itemBuilder: (product, index) {
          return ProductCard(product: product);
        },
      );
    }

    return BaseList(
      data: products,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (product, index) {
        return ProductCard(
          product: product,
          height: 100.h,
        );
      },
    );
  }
}
