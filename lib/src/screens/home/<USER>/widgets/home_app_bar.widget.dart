import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/screens/auth/models/user_model.dart';
import 'package:barber_app/src/screens/auth/view/login/login.screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/theme/color_manager.dart';

class HomeAppbarWidget extends ConsumerWidget {
  const HomeAppbarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final unLoggedWidget = TextButton(
        onPressed: () {
          const LoginScreen().navigate;
        },
        style: ButtonStyle(
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppRadius.radius100),
              side: const BorderSide(
                color: ColorManager.primaryColor,
              ),
            ),
          ),
        ),
        child: Text(
          context.tr.login,
        ));

    final loggedWidget = Row(
      children: [
        Padding(
          padding: const EdgeInsets.all(AppSpaces.padding8),
          child: CircleAvatar(
            backgroundColor: ColorManager.lightPrimaryColor,
            radius: 17.r,
            child: ClipOval(
              child: BaseCachedImage(
                height: 80.h,
                width: 80.w,
                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',
                fit: BoxFit.cover, // Ensures the image covers the circular area
              ),
            ),
          ),
        ),
        Text(
          context.tr.welcomeWithName(UserModel.currentUser.name),
          style: AppTextStyles.title,
        ),
        // const Spacer(),
        // TextButton(
        //     onPressed: () {
        //       ref.watch(authControllerNotifierProvider).logout();
        //     },
        //     style: ButtonStyle(
        //       shape: WidgetStateProperty.all(
        //         RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(AppRadius.radius100),
        //           side: const BorderSide(
        //             color: ColorManager.primaryColor,
        //           ),
        //         ),
        //       ),
        //     ),
        //     child: Text(
        //       context.tr.logout,
        //     ))
      ],
    );

    if (UserModel.isLogged()) {
      return loggedWidget;
    }

    return unLoggedWidget;
  }
}
