import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/container/base_container.widget.dart';
import 'package:barber_app/src/core/shared/widgets/loading/shimmer_loading.widget.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/category/providers/category.providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/lists/base_list.dart';
import '../../../category/models/category.model.dart';
import '../../../category/view/category.screen.dart';

class CategoriesListWidget extends ConsumerWidget {
  const CategoriesListWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getCategoriesFuture = ref.watch(getCategoriesFutureProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(context.tr.products, style: AppTextStyles.title),
        AppGaps.gap8,
        getCategoriesFuture.get(
          loading: () => BaseShimmerLoading(
            child: _buildCategoriesList(
                List.generate(3, (index) => CategoryModel.empty())),
          ),
          data: (data) {
            return _buildCategoriesList(data);
          },
        ),
      ],
    );
  }

  Widget _buildCategoriesList(List<CategoryModel> categories) {
    return BaseList.horizontal(
      height: 45.h,
      data: categories,
      padding: EdgeInsets.zero,
      itemBuilder: (category, index) {
        return BaseContainer(
          onTap: () => CategoriesScreen(
            category: category,
          ).navigate,
          width: 150.w,
          color: ColorManager.black,
          borderColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpaces.padding12,
            vertical: AppSpaces.padding8,
          ),
          child: Row(
            children: [
              category.image?.url.networkImage(
                    height: 40,
                    width: 40,
                    radius: AppRadius.radius100,
                    fit: BoxFit.cover,
                  ) ??
                  ''.networkImage(
                    height: 40,
                    width: 40,
                    radius: AppRadius.radius100,
                    fit: BoxFit.cover,
                  ),
              AppGaps.gap12,
              Expanded(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  alignment: Alignment.centerRight,
                  child: Text(
                    category.name,
                    style: AppTextStyles.subTitle.copyWith(
                      fontSize: 15,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
