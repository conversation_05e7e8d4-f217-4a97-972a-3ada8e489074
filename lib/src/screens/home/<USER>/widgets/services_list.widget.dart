import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/container/base_container.widget.dart';
import 'package:barber_app/src/core/shared/widgets/lists/base_list.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:barber_app/src/screens/product/providers/product.providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/loading/shimmer_loading.widget.dart';
import '../../../service/view/service_details_screen/service_details.screen.dart'
    show ServiceDetailsScreen;

class ServicesListWidget extends ConsumerWidget {
  final String search;
  final bool isHorizontal;

  const ServicesListWidget({
    super.key,
    this.search = '',
    this.isHorizontal = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getServicesFuture = ref.watch(getServicesFutureProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(context.tr.services, style: AppTextStyles.title),
        AppGaps.gap8,
        getServicesFuture.get(
          loading: () => BaseShimmerLoading(
            child: _buildServicesList(
                List.generate(3, (index) => ProductModel.empty())),
          ),
          data: (data) {
            return _buildServicesList(data);
          },
        ),
      ],
    );
  }

  Widget _buildServicesList(List<ProductModel> services) {
    return HookBuilder(builder: (context) {
      final searchedServices = useState<List<ProductModel>>(services);

      if (search.isEmpty) {
        searchedServices.value = services;
      } else {
        searchedServices.value = services
            .where((service) => service.name.toLowerCase().contains(search))
            .toList();
      }

      return BaseList(
        height: isHorizontal ? 45.h : null,
        data: searchedServices.value,
        isHorizontal: isHorizontal,
        padding: EdgeInsets.zero,
        itemBuilder: (service, index) {
          return BaseContainer(
            onTap: () => ServiceDetailsScreen(product: service).navigate,
            width: isHorizontal ? 160.w : double.infinity,
            color: ColorManager.black,
            borderColor: Colors.transparent,
            padding: EdgeInsets.symmetric(
              horizontal: AppSpaces.padding12,
              vertical: isHorizontal ? AppSpaces.padding8 : AppSpaces.padding16,
            ),
            child: Row(
              children: [
                service.thumbnail.url.networkImage(
                  height: isHorizontal ? 40 : 50,
                  width: isHorizontal ? 40 : 50,
                  radius: AppRadius.radius100,
                  fit: BoxFit.cover,
                ),
                AppGaps.gap12,
                Expanded(
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    alignment: Alignment.centerRight,
                    child: Text(
                      service.name,
                      style: isHorizontal
                          ? AppTextStyles.subTitle.copyWith(
                              fontSize: 15,
                            )
                          : AppTextStyles.title,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    });
  }
}
