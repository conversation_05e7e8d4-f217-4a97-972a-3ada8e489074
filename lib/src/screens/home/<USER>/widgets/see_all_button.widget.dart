import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

class SeeAllButtonWidget extends StatelessWidget {
  final Function() onPressed;

  const SeeAllButtonWidget({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return TextButton(
        onPressed: onPressed,
        child: Row(
          children: [
            Text(context.tr.seeAll,
                style: AppTextStyles.labelMedium
                    .copyWith(color: ColorManager.primaryColor)),
            AppGaps.gap8,
            Icon(Icons.arrow_forward_ios, size: 15.r)
          ],
        ));
  }
}
